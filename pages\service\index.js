import siteinfo from '../../siteinfo.js';
import serviceApi from '../../api/modules/service.js';

Page({
  data: {
    userInfo: null,
    siteinfo,
    pets: null,
    typesList: [],
    typeId: null,
    currentTabCode: null,
    currentType: {},
    serviceList: [],
  },

  onLoad(options) {
    const { data } = options;
    this.setData({
      currentTabCode: data,
    });
  },

  onShow() {
    const item = wx.getStorageSync('selectPetInfo');
    this.setData({
      pets: item,
    });
    // 如果有当前选中的类型，重新加载服务列表（宠物信息可能已更新）
    if (this.data.typeId) {
      this.getServiceList(this.data.typeId);
    }
  },

  onShareAppMessage: function () {
    return {
      title: '贝宠约洗，足不出户享精致洗护～',
      path: `/pages/index/index?aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share.png',
    };
  },

  onShareTimeline: function () {
    return {
      title: '「忙到没空送洗？上门服务解忧！」贝宠约洗，专业到家超省心～',
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share1.png',
      query: `aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
    };
  },

  addCurrentPet() {
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index',
      });
      return;
    }
    wx.navigateTo({
      url: '/pages/service/pet/index?isService=true',
    });
  },

  // 监听导航栏切换事件
  handleTabChange(e) {
    const { code } = e.detail;
    this.getTypeList(code);
  },

  // 页面准备就绪后的初始化
  onReady() {
    // 如果导航栏组件还没有触发 tabchange 事件，这里作为备用初始化
    setTimeout(() => {
      if (!this.data.typesList || this.data.typesList.length === 0) {
        // 如果还没有数据，尝试使用当前选中的标签代码或默认值
        const code = this.data.currentTabCode;
        if (code) {
          this.getTypeList(code);
        }
      }
    }, 500); // 给导航栏组件一些时间来触发事件
  },

  // 获取一级类目
  getTypeList(type) {
    if (!type) return;
    serviceApi.list(type).then(res => {
      const typesList = (res.list || []).map(item => ({
        id: item.id,
        avatar: item.avatar,
        type: item.name.slice(0, -2),
        subtype: item.name.slice(-2),
      }));
      this.setData({
        typesList,
        typeId: typesList[0]?.id || null,
        currentType: typesList[0] || {},
      });
      this.getServiceList(typesList[0]?.id || null);
    }).catch(err => {
      console.error('获取服务类目失败:', err);
      // 设置空数据，避免界面异常
      this.setData({
        typesList: [],
        typeId: null,
        currentType: {},
        serviceList: []
      });
    });
  },

  // 一级类目切换
  changeType(evt) {
    let { id } = evt.currentTarget.dataset;
    if (id == this.data.typeId) return;
    this.setData({
      typeId: id,
      currentType: this.data.typesList.find(item => item.id == id) || {},
    });
    this.getServiceList(id);
  },

  // 获取服务列表
  getServiceList(typeId) {
    // 如果 typeId 为空，则不发起请求，避免无效的 API 调用
    if (!typeId) {
      this.setData({
        serviceList: [],
      });
      return;
    }

    const { type, hairType, weightType } = this.data.pets || {};
    let petInfo = {};
    if (type) {
      petInfo['type'] = type;
    }
    if (hairType) {
      petInfo['hairType'] = hairType;
    }
    if (weightType) {
      petInfo['weightType'] = weightType;
    }
    serviceApi.services(typeId, petInfo).then(list => {
      this.setData({
        serviceList: list || [],
      });
    }).catch(err => {
      console.error('获取服务列表失败:', err);
      this.setData({
        serviceList: [],
      });
    });
  },

  redirect(evt) {
    let { item } = evt.currentTarget.dataset;
    wx.setStorageSync('selectServiceInfo', {
      ...item,
      currentType: this.data.currentType,
    });
    wx.navigateTo({
      url: '/pages/service/reservation/index?serviceId=' + item.id,
    });
  },
});
