import request, { analysisRes } from '../request';
import config from '../config';

const { complaint } = config.apiUrls;

export default {
  /**
   * 创建投诉建议
   * @param {string} customerId 客户ID
   * @param {object} complaintData 投诉建议数据
   * @param {string} complaintData.category 大类 (complaint/suggestion)
   * @param {string} complaintData.subCategory 小类 (order/employee/platform/service)
   * @param {string} complaintData.title 标题
   * @param {string} complaintData.content 内容
   * @param {string} complaintData.contactInfo 联系方式
   * @param {number} [complaintData.orderId] 订单ID（可选）
   * @param {number} [complaintData.employeeId] 员工ID（可选）
   * @param {array} [complaintData.photoURLs] 图片URL数组（可选）
   * @returns {Promise<any>} 返回创建结果
   */
  async create(customerId, complaintData) {
    const res = await request.post(
      complaint.create.replace('{customerId}', customerId), 
      complaintData
    );
    return analysisRes(res);
  },

  /**
   * 获取客户投诉建议列表
   * @param {string} customerId 客户ID
   * @param {object} params 查询参数
   * @param {number} [params.page] 页码
   * @param {number} [params.pageSize] 每页数量
   * @param {string} [params.category] 大类过滤
   * @param {string} [params.status] 状态过滤
   * @returns {Promise<any>} 返回投诉建议列表
   */
  async list(customerId, params = {}) {
    const res = await request.get(
      complaint.list.replace('{customerId}', customerId),
      params
    );
    return analysisRes(res);
  },

  /**
   * 获取投诉建议详情
   * @param {string} customerId 客户ID
   * @param {string} complaintId 投诉建议ID
   * @returns {Promise<any>} 返回投诉建议详情
   */
  async detail(customerId, complaintId) {
    const res = await request.get(
      complaint.detail
        .replace('{customerId}', customerId)
        .replace('{complaintId}', complaintId)
    );
    return analysisRes(res);
  },

  /**
   * 更新投诉建议
   * @param {string} customerId 客户ID
   * @param {string} complaintId 投诉建议ID
   * @param {object} complaintData 更新数据
   * @returns {Promise<any>} 返回更新结果
   */
  async update(customerId, complaintId, complaintData) {
    const res = await request.put(
      complaint.update
        .replace('{customerId}', customerId)
        .replace('{complaintId}', complaintId),
      complaintData
    );
    return analysisRes(res);
  },

  /**
   * 删除投诉建议
   * @param {string} customerId 客户ID
   * @param {string} complaintId 投诉建议ID
   * @returns {Promise<any>} 返回删除结果
   */
  async delete(customerId, complaintId) {
    const res = await request.delete(
      complaint.delete
        .replace('{customerId}', customerId)
        .replace('{complaintId}', complaintId)
    );
    return analysisRes(res);
  },

  /**
   * 根据订单获取投诉建议
   * @param {string} orderId 订单ID
   * @returns {Promise<any>} 返回投诉建议列表
   */
  async getByOrderId(orderId) {
    const res = await request.get(
      complaint.orderComplaints.replace('{orderId}', orderId)
    );
    return analysisRes(res);
  }
};
