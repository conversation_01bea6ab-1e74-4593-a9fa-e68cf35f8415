import request, { analysisRes } from '../request';
import config from '../config';

const { coupon } = config.apiUrls;
export default {
  async list() {
    const res = await request.get(coupon.list);
    return analysisRes(res);
  },

  /**
   * 购买优惠券
   * @param {string} customerId 客户ID
   * @param {string} couponId 优惠券ID
   * @param {string} remark 说明说明文字
   * @returns {Promise<any>} 返回订单信息，包含sn等
   */
  async buy(customerId, couponId, remark) {
    const res = await request.post(coupon.buy, {
      customerId,
      couponId,
      remark,
    });
    return analysisRes(res);
  },

  /**
   * 完成支付
   * @param {string} sn 订单号
   * @param {string} cardTypeId 权益卡ID
   * @returns {Promise<any>} 返回订单信息，包含sn等
   */
  async pay(sn, customerId) {
    const res = await request.post(coupon.pay.replace(':sn', sn), { customerId });
    return analysisRes(res);
  },

  /**
   * 获取我的优惠券列表
   * @param {string} customerId 客户ID
   * @returns {Promise<any>} 返回优惠券列表
   */
  async myCoupons(customerId) {
    const res = await request.get(
      coupon.myCoupons.replace('{customerId}', customerId)
    );
    return analysisRes(res);
  },

  /**
   * 获取我的有效优惠券列表
   * @param {string} customerId 客户ID
   * @returns {Promise<any>} 返回有效优惠券列表
   */
  async myValidCoupons(customerId) {
    const res = await request.get(
      coupon.myValidCoupons.replace('{customerId}', customerId)
    );
    return analysisRes(res);
  },

  /**
   * 获取服务可用的优惠券列表
   * @param {string} customerId 客户ID
   * @param {string} serviceId 服务ID
   * @param {number} amount 订单金额
   * @returns {Promise<any>} 返回可用优惠券列表
   */
  async getAvailableCoupons(customerId, serviceId, amount) {
    const res = await request.get(
      coupon.availableCoupons.replace('{customerId}', customerId),
      { serviceId, amount }
    );
    return analysisRes(res);
  }
};
