<view class="container">
  <view wx:if="{{hasRights_nk || hasRights_ck || hasCoupons || hasExpired}}">
    <view wx:if="{{hasRights_nk}}">
      <view class="flex flex-wrap diygw-col-24 justify-center items-center flex22-clz">
        <text class="diygw-col-8 text-clz"> </text>
        <text class="diygw-col-8 text16-clz"> 年卡 </text>
        <text class="diygw-col-8 text-clz"> </text>
      </view>
      <view wx:for="{{rightsList_nk}}" wx:for-index="index" wx:key="id" wx:for-item="item" class="coupon-item" >
        <view class="flex flex-wrap diygw-col-24 flex10-clz">
          <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center flex11-clz">
            <view wx:if="{{!!item.amount}}" >
              <text class="diygw-col-0 text-val">￥ {{item.amount}} </text>
              <text class="diygw-col-0 text-name">{{item.cardType.name}} </text>
            </view>
            <view wx:else>
              <text class="diygw-col-0 text-val">{{item.cardType.name}} </text>
            </view>
          </view>
          <view class="flex flex-wrap diygw-col-0 justify-between items-center flex13-clz">
            <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-start flex14-clz">
              <text class="diygw-col-0 text20-clz"> {{item.cardType.description || ''}} </text>              
              <text wx:if="{{item.remainTimes !== '不限'}}" class="diygw-col-0"> 剩余次数：{{item.remainTimes}} </text>
              <text class="diygw-col-0"> 到期时间：{{item.expiryTime}} </text>
              <text class="diygw-col-0"> 状态：{{item.status}} </text>
            </view>
            <view class="flex flex-wrap diygw-col-0 justify-center items-baseline flex12-clz" bind:tap="goToUse">
              <text class="diygw-col-0 text19-clz"> 去使用 </text>
            </view>
          </view>
        </view>
        <view bindtap="toggleCouponRules" data-id="{{item.id}}" class="coupon-header">
          <!-- <text>使用规则</text>
          <text class="toggle-icon">
            <text wx:if="{{item.showRules}}" class='diy-icon-fold'></text>
            <text wx:else class='diy-icon-unfold' />
          </text> -->
        </view>
      </view>
    </view>

    <view wx:if="{{hasRights_ck}}">
      <view class="flex flex-wrap diygw-col-24 justify-center items-center flex22-clz">
        <text class="diygw-col-8 text-clz"> </text>
        <text class="diygw-col-8 text16-clz"> 次卡 </text>
        <text class="diygw-col-8 text-clz"> </text>
      </view>
      <view wx:for="{{rightsList_ck}}" wx:for-index="index" wx:key="id" wx:for-item="item" class="coupon-item" >
        <view class="flex flex-wrap diygw-col-24 flex10-clz">
          <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center flex11-clz">
            <view wx:if="{{!!item.amount}}" >
              <text class="diygw-col-0 text-val">￥ {{item.amount}} </text>
              <text class="diygw-col-0 text-name">{{item.cardType.name}} </text>
            </view>
            <view wx:else>
              <text class="diygw-col-0 text-val">{{item.cardType.name}} </text>
            </view>
          </view>
          <view class="flex flex-wrap diygw-col-0 justify-between items-center flex13-clz">
            <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-start flex14-clz">
              <text class="diygw-col-0 text20-clz"> {{item.cardType.description || ''}} </text>              
              <text wx:if="{{item.remainTimes !== '不限'}}" class="diygw-col-0"> 剩余次数：{{item.remainTimes}} </text>
              <text class="diygw-col-0"> 到期时间：{{item.expiryTime}} </text>
              <text class="diygw-col-0"> 状态：{{item.status}} </text>
            </view>
            <view class="flex flex-wrap diygw-col-0 justify-center items-baseline flex12-clz" bind:tap="goToUse">
              <text class="diygw-col-0 text19-clz"> 去使用 </text>
            </view>
          </view>
        </view>
        <view bindtap="toggleCouponRules" data-id="{{item.id}}" class="coupon-header">
          <!-- <text>使用规则</text>
          <text class="toggle-icon">
            <text wx:if="{{item.showRules}}" class='diy-icon-fold'></text>
            <text wx:else class='diy-icon-unfold' />
          </text> -->
        </view>
      </view>
    </view>

    <view wx:if="{{hasCoupons}}">
      <view class="flex flex-wrap diygw-col-24 justify-center items-center flex22-clz">
        <text class="diygw-col-8 text-clz"> </text>
        <text class="diygw-col-8 text16-clz"> 优惠券 </text>
        <text class="diygw-col-8 text-clz"> </text>
      </view>
      <view wx:for="{{couponsList}}" wx:for-index="index" wx:key="id" wx:for-item="item" class="coupon-item" >
        <view class="flex flex-wrap diygw-col-24 flex10-clz">
          <view wx:if="{{!!item.amount && !!item.threshold}}" class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center flex11-clz">
            <text class="diygw-col-0 text-val">￥ {{item.amount}} </text>
            <text class="diygw-col-0 text-name">{{item.threshold}} </text>
          </view>
          <view wx:else class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center flex11-clz">
            <text class="diygw-col-0 text-val">{{item.coupon.name}} </text>
          </view>
          <view class="flex flex-wrap diygw-col-0 justify-between items-center flex13-clz">
            <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-start flex14-clz">
              <text class="diygw-col-0 text20-clz"> {{item.coupon.description || ''}} </text>              
              <text wx:if="{{item.remainTimes !== '不限'}}" class="diygw-col-0"> 剩余次数：{{item.remainTimes}} </text>
              <text wx:if="{{!!item.expiryTime}}" class="diygw-col-0"> 到期时间：{{item.expiryTime}} </text>
              <!-- <text class="diygw-col-0"> 状态：{{item.status}} </text> -->
            </view>
            <view class="flex flex-wrap diygw-col-0 justify-center items-baseline flex12-clz" bind:tap="goToUse">
              <text class="diygw-col-0 text19-clz"> 去使用 </text>
            </view>
          </view>
        </view>
        <view bindtap="toggleCouponRules" data-id="{{item.id}}" class="coupon-header">
          <!-- <text>使用规则</text>
          <text class="toggle-icon">
            <text wx:if="{{item.showRules}}" class='diy-icon-fold'></text>
            <text wx:else class='diy-icon-unfold' />
          </text> -->
        </view>
      </view>
    </view>

    <view wx:if="{{hasExpired}}">
      <view class="flex flex-wrap diygw-col-24 justify-center items-center flex22-clz">
        <text class="diygw-col-8 text-clz"> </text>
        <text class="diygw-col-8 text16-clz"> 已失效 </text>
        <text class="diygw-col-8 text-clz"> </text>
      </view>
      <view wx:for="{{expiredList}}" wx:for-index="index" wx:key="id" wx:for-item="item" class="coupon-item" >
        <view class="flex flex-wrap diygw-col-24 flex10-clz">
          <view wx:if="{{!!item.amount && !!item.threshold}}" class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center flex11-clz">
            <text class="diygw-col-0 text-val">￥ {{item.amount}} </text>
            <text class="diygw-col-0 text-name">{{item.threshold}} </text>
          </view>
          <view wx:else class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center flex11-clz">
            <text class="diygw-col-0 text-val">{{item.cardType ? item.cardType.name : item.coupon.name}} </text>
          </view>
          <view class="flex flex-wrap diygw-col-0 justify-between items-center flex13-clz">
            <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-start flex14-clz">
              <text class="diygw-col-0 text20-clz"> {{(item.cardType ? item.cardType.description : item.coupon.description) || ''}} </text>              
              <text wx:if="{{item.remainTimes !== '不限'}}" class="diygw-col-0"> 剩余次数：{{item.remainTimes}} </text>
              <text wx:if="{{!!item.expiryTime}}" class="diygw-col-0"> 到期时间：{{item.expiryTime}} </text>
              <!-- <text class="diygw-col-0"> 状态：{{item.status}} </text> -->
            </view>
          </view>
        </view>
        <view bindtap="toggleCouponRules" data-id="{{item.id}}" class="coupon-header">
          <!-- <text>使用规则</text>
          <text class="toggle-icon">
            <text wx:if="{{item.showRules}}" class='diy-icon-fold'></text>
            <text wx:else class='diy-icon-unfold' />
          </text> -->
        </view>
      </view>
    </view>
  </view>
  <view wx:else class="flex flex-direction-column justify-center items-center  diygw-col-24 flex22-clz empty">
    <image src="https://xian7.zos.ctyun.cn/pet/static/pet.png" style="width: 300rpx; height: 250rpx;" ></image>
    <view>您没有任何卡券</view>
  </view>
  <view class="flex flex-direction-column diygw-col-24 diygw-bottom bottom-container">
    <view class="hidden diygw-form-item diygw-form-item-small diygw-col-24 input-wrapper">
      <view class="input input_clz">
        <input class="flex1" name="input" comfirm-type="done" type="text" value="{{input}}" data-key="input" bindchange="changeInputValue" placeholder="请输入邀请码，领取优惠券" />
        <text class="{{searchAble ? 'btn_lq' :'btn_lq disabled'}}">立即领取</text>
      </view>
    </view>
    <view class="diygw-grid diygw-actions">
      <button class="diygw-action">
        <view class="diygw-grid-inner"  data-type="ticketsRecord" bind:tap="redirect">
          <image src="//xian7.zos.ctyun.cn/pet/static/record.png" class="image7-size diygw-image image-sm" mode="widthFix" />
          <view class="diygw-grid-title"> 使用记录 </view>
        </view>
      </button>
      <button class="diygw-action">
        <view class="diygw-grid-inner"  data-type="rightsCenter" bind:tap="redirect">
          <image src="//xian7.zos.ctyun.cn/pet/static/ticket.png" class="image7-size diygw-image image-sm" mode="widthFix" />
          <view class="diygw-grid-title"> 领券中心 </view>
        </view>
      </button>
    </view>
  </view>
</view>