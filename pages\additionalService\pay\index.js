import additionalServiceApi from '../../../api/modules/additionalService.js';
import payApi from '../../../api/modules/pay.js';
import Session from '../../../common/Session.js';
import utils from '../../utils/util.js';

Page({
  data: {
    userInfo: null,
    orderDetailId: null,
    additionalServiceId: null,
    
    // 追加服务详情
    serviceDetail: null,
    loading: true,
    paying: false,
    
    // 模态框
    showModal: false,
    modalTitle: '',
    modalContent: '',
    modalButtons: []
  },

  onLoad(options) {
    const { orderDetailId, id } = options;
    const userInfo = Session.getUser();
    
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      userInfo,
      orderDetailId: parseInt(orderDetailId),
      additionalServiceId: parseInt(id)
    });

    this.loadServiceDetail();
  },

  /**
   * 加载追加服务详情
   */
  async loadServiceDetail() {
    try {
      this.setData({ loading: true });
      
      const { orderDetailId, additionalServiceId } = this.data;
      const detail = await additionalServiceApi.detail(orderDetailId, additionalServiceId);
      
      if (detail) {
        // 检查状态是否可以支付
        if (detail.status !== 'confirmed') {
          wx.showToast({
            title: '当前状态不支持支付',
            icon: 'none'
          });
          wx.navigateBack();
          return;
        }
        
        // 格式化数据
        const formattedDetail = {
          ...detail,
          createdAt: detail.createdAt ? utils.formatNormalDate(detail.createdAt) : '',
          confirmTime: detail.confirmTime ? utils.formatNormalDate(detail.confirmTime) : ''
        };
        
        this.setData({ serviceDetail: formattedDetail });
      } else {
        wx.showToast({
          title: '服务详情不存在',
          icon: 'none'
        });
        wx.navigateBack();
      }
      
    } catch (error) {
      console.error('加载追加服务详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      wx.navigateBack();
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 确认支付
   */
  confirmPay() {
    const { serviceDetail } = this.data;
    
    if (!serviceDetail) return;
    
    this.setData({
      showModal: true,
      modalTitle: '确认支付',
      modalContent: `确认支付 ¥${serviceDetail.totalFee} 吗？`,
      modalButtons: [
        {
          text: '取消',
          type: 'cancel',
          event: 'handleModalCancel'
        },
        {
          text: '确认支付',
          type: 'primary',
          event: 'handlePayConfirm'
        }
      ]
    });
  },

  /**
   * 处理支付确认
   */
  async handlePayConfirm() {
    this.setData({ showModal: false });
    await this.processPay();
  },

  /**
   * 处理支付
   */
  async processPay() {
    const { userInfo, orderDetailId, additionalServiceId, serviceDetail } = this.data;
    
    if (this.data.paying) return;
    
    try {
      this.setData({ paying: true });
      
      // 处理0元订单
      if (Number(serviceDetail.totalFee) === 0) {
        wx.showLoading({ title: '处理中...' });
        
        const result = await additionalServiceApi.pay(orderDetailId, additionalServiceId, userInfo.id);
        
        wx.hideLoading();
        
        if (result) {
          this.showPaymentSuccessModal();
        } else {
          wx.showToast({
            title: '支付失败，请重试',
            icon: 'none'
          });
        }
        return;
      }
      
      // 正常支付流程
      // 注意：这里需要根据实际的支付流程调整
      // 可能需要先获取支付参数，然后调用微信支付
      wx.showLoading({ title: '发起支付...' });
      
      // 调用支付接口
      const result = await additionalServiceApi.pay(orderDetailId, additionalServiceId, userInfo.id);
      
      wx.hideLoading();
      
      if (result) {
        // 如果后端直接处理支付成功
        this.showPaymentSuccessModal();
      } else {
        // 如果需要调用微信支付
        // 这里应该根据实际情况调用 payApi.doPay
        wx.showToast({
          title: '支付失败，请重试',
          icon: 'none'
        });
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('支付失败:', error);
      wx.showToast({
        title: '支付失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ paying: false });
    }
  },

  /**
   * 显示支付成功模态框
   */
  showPaymentSuccessModal() {
    this.setData({
      showModal: true,
      modalTitle: '支付成功',
      modalContent: '您的追加服务已支付成功，服务人员将为您提供服务',
      modalButtons: [
        {
          text: '确定',
          type: 'primary',
          event: 'handlePaymentSuccess'
        }
      ]
    });
  },

  /**
   * 处理支付成功
   */
  handlePaymentSuccess() {
    this.setData({ showModal: false });
    
    // 返回到追加服务列表页面
    wx.navigateBack({
      delta: 2 // 返回两级，跳过详情页
    });
  },

  /**
   * 查看服务详情
   */
  viewServiceDetail() {
    const { orderDetailId, additionalServiceId } = this.data;
    
    wx.navigateTo({
      url: `/pages/additionalService/detail/index?orderDetailId=${orderDetailId}&id=${additionalServiceId}`
    });
  },

  /**
   * 模态框取消
   */
  handleModalCancel() {
    this.setData({ showModal: false });
  }
});
