# 追加服务模块实现文档

## 概述

追加服务模块允许用户在订单服务进行中时申请额外的服务项目，经过员工确认后进行支付并享受服务。

## 功能特性

### 1. 用户端功能
- **申请追加服务**: 在订单状态为"服务中"时可申请追加服务
- **服务选择**: 选择可用的追加服务项目和数量
- **优惠使用**: 支持使用权益卡和优惠券
- **查看申请记录**: 查看所有追加服务申请的状态和详情
- **在线支付**: 员工确认后可进行在线支付

### 2. 业务流程
1. **用户申请阶段**
   - 用户在订单详情页点击"申请追加服务"
   - 选择需要的服务项目和数量
   - 可选择使用权益卡或优惠券
   - 填写备注信息并提交申请

2. **员工确认阶段**
   - 员工收到申请通知
   - 可以确认或拒绝申请
   - 拒绝时需要填写拒绝原因

3. **用户支付阶段**
   - 员工确认后用户收到通知
   - 用户进行支付操作
   - 支付成功后状态变为"已付款/服务中"

## 技术实现

### 1. API 模块
- `api/modules/additionalService.js` - 追加服务相关API调用
- 支持创建申请、查询列表、查询详情、支付等操作

### 2. 页面结构
```
pages/additionalService/
├── apply/          # 申请追加服务页面
├── list/           # 追加服务列表页面
├── detail/         # 追加服务详情页面
└── pay/            # 支付页面
```

### 3. 状态管理
追加服务状态定义：
- `pending_confirm` - 待确认
- `confirmed` - 已确认
- `paid` - 已付款
- `rejected` - 已拒绝

### 4. 数据流
1. **申请创建**: 用户选择服务 → 计算价格 → 提交申请
2. **状态查询**: 实时查询申请状态和详情
3. **支付处理**: 确认后发起支付 → 更新状态

## 接口说明

### 1. 创建追加服务申请
```
POST /order-details/{orderDetailId}/additional-services
```

### 2. 查询追加服务列表
```
GET /order-details/{orderDetailId}/additional-services
```

### 3. 查询追加服务详情
```
GET /order-details/{orderDetailId}/additional-services/{id}
```

### 4. 支付追加服务订单
```
POST /order-details/{orderDetailId}/additional-services/{id}/pay
```

### 5. 获取可用服务/优惠
```
GET /openapi/service/additional-service/{serviceId}  # 通过服务ID获取增项服务
GET /customers/{customerId}/available-cards
GET /customers/{customerId}/available-coupons
```

**注意**: 获取增项服务需要先从订单详情中获取主服务ID，然后调用 `/openapi/service/additional-service/{serviceId}` 接口。

## 用户界面

### 1. 申请页面特性
- 服务选择卡片式布局
- 实时价格计算
- 优惠选择弹窗
- 表单验证和提交

### 2. 列表页面特性
- 状态筛选标签
- 卡片式展示
- 下拉刷新
- 操作按钮（支付、查看详情）

### 3. 详情页面特性
- 状态卡片展示
- 完整信息展示
- 操作按钮（支付、联系客服）

### 4. 支付页面特性
- 订单信息确认
- 支付方式选择
- 安全支付流程

## 集成说明

### 1. 订单详情页集成
在订单状态为"服务中"时显示追加服务相关按钮：
- "申请追加服务" - 跳转到申请页面
- "查看追加服务" - 跳转到列表页面

### 2. 支付系统集成
- 支持0元订单直接处理
- 集成微信支付流程
- 支付状态回调处理

### 3. 优惠系统集成
- 权益卡自动应用最高折扣
- 优惠券手动选择
- 不能同时使用权益卡和优惠券

## 注意事项

1. **状态限制**: 只有"服务中"状态的订单才能申请追加服务
2. **权限控制**: 只有订单的客户本人才能申请追加服务
3. **价格计算**: 先应用权益卡折扣，再应用优惠券抵扣
4. **支付安全**: 支付前需要再次确认订单信息
5. **错误处理**: 完善的错误提示和异常处理

## 扩展功能

### 未来可扩展的功能
1. **批量申请**: 支持一次申请多个不同类型的服务
2. **预约时间**: 为追加服务指定具体的服务时间
3. **服务评价**: 对追加服务进行单独评价
4. **退款功能**: 支持追加服务的退款申请
5. **推送通知**: 状态变更时的实时推送通知
