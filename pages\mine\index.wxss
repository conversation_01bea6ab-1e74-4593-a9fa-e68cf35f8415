/* pages/mine/index.wxss */
.containermine {
  background-color: #f5f5f5;
  padding: 24rpx;
  padding-bottom: 160rpx;
  background: url('https://xian7.zos.ctyun.cn/pet/static/minebj.png') no-repeat;
  background-size: contain;
}

.flex12-clz {
  width: calc(100% - 40rpx) !important;
  margin: 20rpx 20rpx 40rpx;
}
.btn-login{
  background: rgba(255, 67, 145, 1);
  box-shadow: 0px 3px 0px 0px rgba(255, 67, 145, 0.3);
  color: #fff;
  border-radius: 40rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  padding: 0 40rpx;
}
.flex10-clz {
  color: #333;
}

.image-round {
  border-radius: 120rpx;
  overflow: hidden;
}
.image7-size {
  height: 120rpx !important;
  width: 120rpx !important;
}

.flex20-clz {
  padding: 0rpx 24rpx;
  flex: 1;
}

.flex13-clz {
  flex: 1;
}

.text12-clz {
  font-weight: bold;
  font-size: 32rpx !important;
}

.flex14-clz {
  flex: 1;
  color: rgba(153, 153, 153, 1);
}

.icon5 {
  font-size: 40rpx;
}

.vipbg{
  clear: both;
  position: relative;
  height:fit-content;
}
.vip-contain{
  position: absolute;
  top:30rpx;
  left:30rpx;
  right:30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.viptext{
  color: rgba(47, 131, 255, 1);
  font-weight: bold;
  font-size: 30rpx;
  font-style: italic;
}
.btn{
  height: 60rpx;
  background-color: rgba(255, 67, 143, 1);
  border-radius: 60rpx;
  line-height: 60rpx;
  padding: 0 28rpx;
  color: white;
  display: flex;
  align-items: center;
}

.image-govip{
  width: 30rpx !important;
  margin-left: 10rpx;
}

.flex16-clz {
  clear: both;
  margin: -28rpx 0 30rpx;
  background-color: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  padding: 30rpx;
}

.flex17-clz {
  padding: 0;
}

.text13-clz {
  color: #333;
  font-weight: bold;
  font-size: 34rpx !important;
}

.flex30-clz {
  color: rgba(153, 153, 153, 1);
  padding: 10rpx 0;
  border-radius: 32rpx;
}

.text32-clz {
  text-align: right;
}

.flex6-clz {
  margin: 10rpx 0rpx 10rpx -30rpx;
  width: calc(100% + 30rpx - 0rpx) !important;
}

.flex46-clz {
  padding: 30rpx;
  border-radius: 24rpx;
  margin: 15rpx 0rpx 0rpx 30rpx;
  background-color: rgba(255, 192, 218, 0.3);
  overflow: hidden;
  width: calc(70% - 20rpx - 0rpx) !important;
}

.image7-clz {
  border-radius: 24rpx;
  overflow: hidden;
}

.image7-size {
  height: 160rpx !important;
  width: 160rpx !important;
}

.flex48-clz {
  padding-top: 0rpx;
  flex: 1;
  padding-left: 20rpx;
  padding-bottom: 0rpx;
  padding-right: 0rpx;
}

.text23-clz {
  font-size: 30rpx !important;
  font-weight: bold;
  color: #333;
}

.flex49-clz {
  margin: 6rpx 0;
  width: calc(100% - 0rpx - 0rpx) !important;
  flex-direction: column;
}

.text24-pet-ly {
  line-height: 50rpx;
  font-size: 24rpx !important;
  color: #666;
}

.flex38-clz{
  margin: 0 0 30rpx;
  background-color: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  padding: 30rpx;
}
.flex21-clz {
  padding: 0 0 24rpx;
}

.text-clz {
  padding: 0rpx;
  font-weight: bold;
  font-size: 32rpx !important;
}

.flex17-clz {
  color: #838383;
}

.icon6 {
  font-size: 32rpx;
}

.flex18-clz {
  padding: 10rpx;
}

.image-size {
  height: 80rpx !important;
  width: 80rpx !important;
}
.image-sm{
  height: 60rpx !important;
  width: 60rpx !important;
}
.text11-clz {
  padding: 10rpx;
}

/* 重置button样式为card */
.card {
  background-color: white;
  line-height: normal;
}
.card::after {
  border: none;
}

/* 推广员工信息样式 */
.promotion-employee-info {
  margin: 0 0 30rpx;
  background-color: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  padding: 30rpx;
}

.promotion-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.employee-card {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  color: white;
}

.employee-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.employee-details {
  flex: 1;
}

.employee-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.employee-phone {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.employee-rating {
  display: flex;
  gap: 20rpx;
}

.rating-text,
.level-text {
  font-size: 22rpx;
  opacity: 0.9;
}

/* 推广码弹窗样式 */
.promotion-modal-content {
  padding: 20rpx 0;
}

.promotion-tip {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}

.promotion-input-wrapper {
  margin-bottom: 20rpx;
}

.promotion-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.promotion-input:focus {
  border-color: #667eea;
}

.checking-status {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  text-align: center;
}

.code-status {
  font-size: 24rpx;
  margin-top: 10rpx;
  text-align: center;
}

.code-status .success {
  color: #07c160;
}

.code-status .error {
  color: #ee0a24;
}

.promotion-notice {
  background: #fff7e6;
  border: 1rpx solid #ffd591;
  border-radius: 8rpx;
  padding: 20rpx;
}

.notice-text {
  font-size: 24rpx;
  color: #d48806;
  line-height: 1.5;
}