import additionalServiceApi from '../../../api/modules/additionalService.js';
import serviceApi from '../../../api/modules/service.js';
import orderApi from '../../../api/modules/order.js';
import rightsCardApi from '../../../api/modules/rightsCard.js';
import couponApi from '../../../api/modules/coupon.js';
import Session from '../../../common/Session.js';
import utils from '../../utils/util.js';
import safeApi from '../../../utils/safe-api.js';

Page({
  data: {
    userInfo: null,
    orderDetailId: null,
    orderDetail: null,
    serviceId: null, // 主服务ID，用于获取增项服务

    // 可用服务列表
    availableServices: [],
    selectedServices: [], // 已选择的服务 [{serviceId, quantity, service}]
    
    // 优惠信息
    availableCards: [],
    availableCoupons: [],
    bestDiscountCard: null, // 最优折扣卡（自动应用）
    selectedCardId: '',
    selectedCouponId: '',
    
    // 价格计算
    originalPrice: 0,
    totalFee: 0,
    cardDeduction: 0,
    couponDeduction: 0,
    
    // 备注
    remark: '',
    
    // UI状态
    loading: false,
    showDiscountSelector: false,
    hasExistingApplication: false, // 是否已有申请记录
    existingServices: [], // 已申请的服务
    
    // 模态框
    showModal: false,
    modalTitle: '',
    modalContent: '',
    modalButtons: []
  },

  onLoad(options) {
    try {
      const { orderDetailId } = options;
      const userInfo = Session.getUser();

      if (!userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        wx.navigateBack();
        return;
      }

      if (!orderDetailId) {
        wx.showToast({
          title: '订单信息不存在',
          icon: 'none'
        });
        wx.navigateBack();
        return;
      }

      this.setData({
        userInfo,
        orderDetailId: parseInt(orderDetailId)
      });

      // 延迟加载数据，避免 JSBridge 错误
      setTimeout(() => {
        this.loadData();
      }, 100);
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '页面初始化失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载页面数据
   */
  async loadData() {
    try {
      // 使用更安全的加载提示
      this.setData({ loading: true });

      // 先加载订单详情获取服务ID
      await this.loadOrderDetail();

      // 然后串行加载，避免并发请求导致的 JSBridge 错误
      await this.loadAvailableServices();
      await this.loadAvailableDiscounts();
      await this.checkExistingApplications();

    } catch (error) {
      console.error('加载数据失败:', error);
      // 使用更安全的错误提示
      this.setData({
        showModal: true,
        modalTitle: '加载失败',
        modalContent: '数据加载失败，请重试',
        modalButtons: [
          {
            text: '重试',
            type: 'primary',
            event: 'retryLoad'
          },
          {
            text: '返回',
            type: 'default',
            event: 'goBack'
          }
        ]
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 重试加载
   */
  retryLoad() {
    this.setData({ showModal: false });
    setTimeout(() => {
      this.loadData();
    }, 500);
  },

  /**
   * 返回上一页
   */
  goBack() {
    this.setData({ showModal: false });
    wx.navigateBack();
  },

  /**
   * 加载订单详情
   */
  async loadOrderDetail() {
    try {
      const { userInfo, orderDetailId } = this.data;

      // 使用安全的API调用
      const orderList = await safeApi.safeApiCall(
        () => orderApi.getlists(userInfo.id, 'all'),
        { errorMessage: '获取订单列表失败' }
      );

      // 查找包含该orderDetailId的订单
      let targetOrder = null;
      let targetOrderDetail = null;

      for (const order of orderList || []) {
        const orderDetail = order.orderDetails?.find(detail => detail.id === orderDetailId);
        if (orderDetail) {
          targetOrder = order;
          targetOrderDetail = orderDetail;
          break;
        }
      }

      if (targetOrderDetail && targetOrderDetail.service) {
        this.setData({
          orderDetail: targetOrderDetail,
          serviceId: targetOrderDetail.service.id
        });
      } else {
        throw new Error('未找到对应的订单详情');
      }
    } catch (error) {
      console.error('加载订单详情失败:', error);
      throw error;
    }
  },

  /**
   * 加载可用服务
   */
  async loadAvailableServices() {
    try {
      const { serviceId } = this.data;

      if (!serviceId) {
        console.warn('服务ID不存在，无法加载增项服务');
        this.setData({ availableServices: [] });
        return;
      }

      // 使用现有的通过服务ID获取增项服务的接口
      const services = await serviceApi.getAdditionalService(serviceId);

      // 按照正常下单时的逻辑进行分组
      const dictionary = wx.getStorageSync('dictionary_com');
      const originalArray = services || [];
      const groupedArray = originalArray.reduce((acc, current) => {
        // 如果累加器中还没有这个type的数组，就创建一个
        if (!acc[current.type]) {
          acc[current.type] = {
            name: dictionary.find(v => v.code === current.type)?.name || current.type,
            id: current.type,
            children: [],
          };
        }
        // 将当前对象push到对应type的数组中
        acc[current.type].children.push(current);
        return acc;
      }, {});

      // 转换为数组
      const groupedList = Object.values(groupedArray);

      this.setData({
        availableServices: groupedList || []
      });

      // 更新服务选择状态
      this.updateServiceSelectionStatus();
    } catch (error) {
      console.error('加载可用服务失败:', error);
      throw error;
    }
  },

  /**
   * 加载可用优惠信息
   */
  async loadAvailableDiscounts() {
    try {
      const { userInfo, selectedServices, serviceId } = this.data;

      // 如果没有主服务ID，无法查询优惠信息
      if (!serviceId) {
        console.warn('主服务ID不存在，无法查询优惠信息');
        this.setData({
          availableCards: [],
          availableCoupons: []
        });
        return;
      }

      // 计算当前选择服务的总金额，用于查询可用的卡券
      // 如果还没有选择服务，使用默认金额1元来查询所有可能的优惠
      const totalAmount = selectedServices.reduce((total, item) => {
        return total + (item.service.price * item.quantity);
      }, 0) || 1;

      const [cardsResult, couponsResult] = await Promise.all([
        rightsCardApi.getAvailableCards(userInfo.id, serviceId, totalAmount),
        couponApi.getAvailableCoupons(userInfo.id, serviceId, totalAmount)
      ]);

      // 过滤权益卡，只保留折扣卡，排除次卡（追加服务不支持次卡）
      let availableCards = [];
      let bestDiscountCard = null;
      if (cardsResult && cardsResult.list && cardsResult.list.length > 0) {
        availableCards = cardsResult.list.filter(card => {
          return card.cardType && card.cardType.type === 'discount';
        }).map(card => ({
          ...card,
          expiryTime: utils.formatNormalDate(card.expiryTime)
        }));

        // 找出最优的折扣卡（折扣力度最大的）
        if (availableCards.length > 0) {
          bestDiscountCard = availableCards.reduce((best, current) => {
            // 折扣率越小，优惠力度越大
            return best.cardType.discountRate < current.cardType.discountRate ? best : current;
          }, availableCards[0]);
        }
      }

      // 处理优惠券数据
      let availableCoupons = [];
      if (couponsResult && couponsResult.list && couponsResult.list.length > 0) {
        availableCoupons = couponsResult.list.map(coupon => ({
          ...coupon,
          threshold: Number(coupon.coupon.threshold || '0'),
          amount: Number(coupon.coupon.amount || '0'),
          expiryTime: utils.formatNormalDate(coupon.expiryTime)
        }));
      }

      this.setData({
        availableCards: [], // 不显示折扣卡选择，自动应用最优折扣卡
        availableCoupons,
        bestDiscountCard, // 保存最优折扣卡信息
        selectedCardId: bestDiscountCard ? bestDiscountCard.id : '' // 自动选择最优折扣卡
      });
    } catch (error) {
      console.error('加载优惠信息失败:', error);
      // 如果加载失败，设置为空数组，不影响其他功能
      this.setData({
        availableCards: [],
        availableCoupons: []
      });
    }
  },

  /**
   * 检查已有申请记录
   */
  async checkExistingApplications() {
    try {
      const { orderDetailId } = this.data;

      // 查询该订单详情下的追加服务申请
      const applications = await additionalServiceApi.list(orderDetailId);

      if (applications && applications.length > 0) {
        // 查找待确认或已确认的申请（排除已拒绝的）
        const pendingOrConfirmedApps = applications.filter(app =>
          app.status === 'pending_confirm' || app.status === 'confirmed' || app.status === 'paid'
        );

        if (pendingOrConfirmedApps.length > 0) {
          // 有待处理的申请，回填数据并禁用提交
          const latestApp = pendingOrConfirmedApps[0]; // 取最新的申请

          // 获取当前可用的增项服务列表，用于匹配正确的服务名称
          const { serviceId } = this.data;
          let availableAdditionalServices = [];
          try {
            if (serviceId) {
              availableAdditionalServices = await serviceApi.getAdditionalService(serviceId);
            }
          } catch (error) {
            console.warn('获取增项服务列表失败:', error);
          }

          // 构建已选服务数据，优先使用增项服务列表中的正确名称
          const existingServices = latestApp.details.map(detail => {
            // 尝试从增项服务列表中找到对应的服务
            const matchedService = availableAdditionalServices.find(service => service.id === detail.serviceId);

            return {
              serviceId: detail.serviceId,
              quantity: detail.quantity,
              service: {
                id: detail.serviceId,
                name: matchedService ? matchedService.name : detail.serviceName, // 优先使用增项服务的正确名称
                price: detail.servicePrice
              }
            };
          });

          // 回填优惠券选择（如果有的话）
          let selectedCouponId = '';
          if (latestApp.discountInfos && latestApp.discountInfos.length > 0) {
            const couponDiscount = latestApp.discountInfos.find(info => info.discountType === 'coupon');
            if (couponDiscount) {
              selectedCouponId = couponDiscount.discountId;
            }
          }

          // 添加调试信息（仅在开发环境）
          if (typeof __wxConfig !== 'undefined' && __wxConfig.envVersion === 'develop') {
            console.log('后端返回的追加服务数据:', latestApp);
            console.log('价格信息:', {
              originalPrice: latestApp.originalPrice,
              cardDeduction: latestApp.cardDeduction,
              couponDeduction: latestApp.couponDeduction,
              totalFee: latestApp.totalFee
            });
          }

          // 确保优惠金额为正数（前端显示时会加负号）
          const cardDeduction = Math.abs(latestApp.cardDeduction || 0);
          const couponDeduction = Math.abs(latestApp.couponDeduction || 0);

          this.setData({
            hasExistingApplication: true,
            existingServices,
            selectedServices: existingServices,
            selectedCouponId,
            remark: latestApp.remark || '',
            // 直接使用后端返回的价格信息，不重新计算
            originalPrice: (latestApp.originalPrice || 0).toFixed(2),
            cardDeduction: cardDeduction.toFixed(2),
            couponDeduction: couponDeduction.toFixed(2),
            totalFee: (latestApp.totalFee || 0).toFixed(2)
          });

          // 更新服务选择状态
          this.updateServiceSelectionStatus();

          // 注意：这里不调用 calculatePrice()，因为我们直接使用后端返回的价格数据
        }
      }
    } catch (error) {
      console.error('检查已有申请失败:', error);
      // 检查失败不影响正常流程
    }
  },

  /**
   * 选择服务
   */
  async selectService(e) {
    const { service } = e.currentTarget.dataset;
    const { selectedServices, hasExistingApplication } = this.data;

    // 如果已有申请记录，禁用选择
    if (hasExistingApplication) {
      wx.showToast({
        title: '已有申请记录，无法修改',
        icon: 'none'
      });
      return;
    }

    // 检查是否已选择
    const existingIndex = selectedServices.findIndex(item => item.serviceId === service.id);

    if (existingIndex >= 0) {
      // 已选择，增加数量
      const updatedServices = [...selectedServices];
      updatedServices[existingIndex].quantity += 1;
      this.setData({ selectedServices: updatedServices });
    } else {
      // 未选择，添加到列表
      const newService = {
        serviceId: service.id,
        quantity: 1,
        service: service
      };
      this.setData({
        selectedServices: [...selectedServices, newService]
      });
    }

    this.calculatePrice();

    // 更新服务选择状态显示
    this.updateServiceSelectionStatus();

    // 重新加载优惠信息（基于新的服务金额）
    await this.loadAvailableDiscounts();

    // 选择服务后自动滚动到已选服务区域
    this.scrollToSelectedServices();
  },

  /**
   * 更新服务选择状态显示
   */
  updateServiceSelectionStatus() {
    const { availableServices, selectedServices } = this.data;

    // 为每个服务添加选择状态
    const updatedServices = availableServices.map(group => ({
      ...group,
      children: group.children.map(service => {
        const selectedService = selectedServices.find(item => item.serviceId === service.id);
        return {
          ...service,
          isSelected: !!selectedService,
          selectedQuantity: selectedService ? selectedService.quantity : 0
        };
      })
    }));

    this.setData({
      availableServices: updatedServices
    });
  },

  /**
   * 滚动到已选服务区域
   */
  scrollToSelectedServices() {
    // 延迟执行，确保DOM更新完成
    setTimeout(() => {
      wx.createSelectorQuery()
        .select('#selected-services-section')
        .boundingClientRect((rect) => {
          if (rect) {
            wx.pageScrollTo({
              scrollTop: rect.top + wx.getSystemInfoSync().scrollTop - 100, // 留一些边距
              duration: 300
            });
          }
        })
        .exec();
    }, 100);
  },

  /**
   * 调整服务数量
   */
  async adjustServiceQuantity(e) {
    const { serviceId, action } = e.currentTarget.dataset;
    const { selectedServices, hasExistingApplication } = this.data;

    // 如果已有申请记录，禁用调整
    if (hasExistingApplication) {
      wx.showToast({
        title: '已有申请记录，无法修改',
        icon: 'none'
      });
      return;
    }

    const updatedServices = selectedServices.map(item => {
      if (item.serviceId === serviceId) {
        const newQuantity = action === 'increase' ? item.quantity + 1 : item.quantity - 1;
        return { ...item, quantity: Math.max(0, newQuantity) };
      }
      return item;
    }).filter(item => item.quantity > 0); // 移除数量为0的服务

    this.setData({ selectedServices: updatedServices });
    this.calculatePrice();

    // 更新服务选择状态显示
    this.updateServiceSelectionStatus();

    // 重新加载优惠信息（基于新的服务金额）
    await this.loadAvailableDiscounts();
  },

  /**
   * 计算价格
   */
  calculatePrice() {
    const { selectedServices, selectedCouponId, availableCoupons, bestDiscountCard, hasExistingApplication } = this.data;

    // 如果已有申请记录，不重新计算价格
    if (hasExistingApplication) {
      return;
    }

    // 计算原价
    const originalPrice = selectedServices.reduce((total, item) => {
      return total + (item.service.price * item.quantity);
    }, 0);

    // 计算权益卡抵扣（自动应用最优折扣卡）
    let cardDeduction = 0;
    if (bestDiscountCard && bestDiscountCard.cardType && bestDiscountCard.cardType.type === 'discount') {
      // 折扣率是小数形式，如0.8表示8折，所以抵扣金额 = 原价 * (1 - 折扣率)
      cardDeduction = originalPrice * (1 - bestDiscountCard.cardType.discountRate);
    }

    // 计算优惠券抵扣
    let couponDeduction = 0;
    if (selectedCouponId) {
      const selectedCoupon = availableCoupons.find(coupon => coupon.id === selectedCouponId);
      if (selectedCoupon) {
        couponDeduction = Math.min(selectedCoupon.amount, originalPrice - cardDeduction);
      }
    }

    const totalFee = Math.max(0, originalPrice - cardDeduction - couponDeduction);

    this.setData({
      originalPrice: originalPrice.toFixed(2),
      cardDeduction: cardDeduction.toFixed(2),
      couponDeduction: couponDeduction.toFixed(2),
      totalFee: totalFee.toFixed(2)
    });
  },

  /**
   * 显示优惠选择器
   */
  showDiscountSelector() {
    const { hasExistingApplication } = this.data;

    // 如果已有申请记录，禁用选择
    if (hasExistingApplication) {
      wx.showToast({
        title: '已有申请记录，无法修改优惠券',
        icon: 'none'
      });
      return;
    }

    this.setData({ showDiscountSelector: true });
  },

  /**
   * 隐藏优惠选择器
   */
  hideDiscountSelector() {
    this.setData({ showDiscountSelector: false });
  },



  /**
   * 选择优惠券
   */
  selectCoupon(e) {
    const { couponId } = e.currentTarget.dataset;
    const { selectedCouponId, hasExistingApplication } = this.data;

    // 如果已有申请记录，禁用选择
    if (hasExistingApplication) {
      wx.showToast({
        title: '已有申请记录，无法修改优惠券',
        icon: 'none'
      });
      return;
    }

    this.setData({
      selectedCouponId: selectedCouponId === couponId ? '' : couponId
    });

    this.calculatePrice();
  },

  /**
   * 备注输入
   */
  onRemarkInput(e) {
    this.setData({ remark: e.detail.value });
  },

  /**
   * 提交申请
   */
  async submitApplication() {
    const { selectedServices, userInfo, orderDetailId, selectedCouponId, remark, bestDiscountCard, availableCoupons, hasExistingApplication } = this.data;

    // 如果已有申请记录，禁用提交
    if (hasExistingApplication) {
      wx.showToast({
        title: '已有申请记录，无法重复提交',
        icon: 'none'
      });
      return;
    }

    if (selectedServices.length === 0) {
      wx.showToast({
        title: '请选择要追加的服务',
        icon: 'none'
      });
      return;
    }
    
    try {
      wx.showLoading({ title: '提交中...' });
      
      // 构建请求数据
      const requestData = {
        customerId: userInfo.id,
        services: selectedServices.map(item => ({
          serviceId: item.serviceId,
          quantity: item.quantity
        })),
        remark
      };
      
      // 添加优惠信息
      const discountInfos = [];
      if (bestDiscountCard) {
        discountInfos.push({
          discountType: 'membership_card',
          discountId: bestDiscountCard.id,
          discountAmount: parseFloat(this.data.cardDeduction)
        });
      }
      
      if (selectedCouponId) {
        const selectedCoupon = availableCoupons.find(coupon => coupon.id === selectedCouponId);
        if (selectedCoupon) {
          discountInfos.push({
            discountType: 'coupon',
            discountId: selectedCouponId,
            discountAmount: parseFloat(this.data.couponDeduction)
          });
        }
      }
      
      if (discountInfos.length > 0) {
        requestData.discountInfos = discountInfos;
      }
      
      // 提交申请
      await additionalServiceApi.create(orderDetailId, requestData);
      
      wx.hideLoading();
      
      // 显示成功提示
      this.setData({
        showModal: true,
        modalTitle: '申请成功',
        modalContent: '您的追加服务申请已提交，请等待员工确认',
        modalButtons: [
          {
            text: '确定',
            type: 'primary',
            event: 'handleModalConfirm'
          }
        ]
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('提交申请失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 模态框确认
   */
  handleModalConfirm() {
    const { modalButtons } = this.data;
    const confirmButton = modalButtons.find(btn => btn.type === 'primary');

    if (confirmButton && confirmButton.event === 'retryLoad') {
      this.retryLoad();
    } else {
      this.setData({ showModal: false });
      // 跳转到追加服务列表页面
      const { orderDetailId } = this.data;
      wx.redirectTo({
        url: `/pages/additionalService/list/index?orderDetailId=${orderDetailId}`
      });
    }
  },

  /**
   * 模态框取消
   */
  handleModalCancel() {
    const { modalButtons } = this.data;
    const cancelButton = modalButtons.find(btn => btn.type === 'default');

    if (cancelButton && cancelButton.event === 'goBack') {
      this.goBack();
    } else {
      this.setData({ showModal: false });
    }
  },

  /**
   * 查看申请记录
   */
  viewApplicationList() {
    const { orderDetailId } = this.data;
    wx.navigateTo({
      url: `/pages/additionalService/list/index?orderDetailId=${orderDetailId}`
    });
  }
});
