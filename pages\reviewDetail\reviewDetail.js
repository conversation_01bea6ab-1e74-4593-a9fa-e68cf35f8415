// pages/reviewDetail/reviewDetail.js
import orderApi from '../../api/modules/order';
import reviewApi from '../../api/modules/review';
import utils from '../utils/util';

Page({
  data: {
    orderId: '',
    orderDetail: null,
    reviewData: null,
    servicePhotos: null, // 服务照片
    loading: true,
    ratingTexts: ['很差', '较差', '一般', '满意', '非常满意'],
    userInfo: null,
  },

  onLoad(options) {
    const { orderId } = options;
    if (orderId) {
      this.setData({ orderId });
      this.loadData();
    }
    
    // 获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({ userInfo });
    }
  },

  /**
   * 加载数据
   */
  async loadData() {
    try {
      wx.showLoading({ title: '加载中...' });
      const { userInfo, orderId } = this.data;
      
      if (!userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      // 并行加载订单详情和评价数据
      const [orderDetail, reviewData] = await Promise.all([
        orderApi.getDetail(userInfo.id, orderId),
        this.loadReviewData(orderId)
      ]);

      // 格式化订单时间
      if (orderDetail && orderDetail.createdAt) {
        orderDetail.createdAt = utils.formatNormalDate(orderDetail.createdAt);
      }

      // 格式化评价时间
      if (reviewData && reviewData.createdAt) {
        reviewData.createdAt = utils.formatNormalDate(reviewData.createdAt);
      }

      this.setData({
        orderDetail,
        reviewData,
        loading: false
      });

      // 如果订单状态是已完成或已评价，加载服务照片
      if (orderDetail && (orderDetail.status === '已完成' || orderDetail.status === '已评价')) {
        this.loadServicePhotos(orderId);
      }
      
    } catch (error) {
      console.error('加载数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 加载评价数据
   */
  async loadReviewData(orderId) {
    try {
      const reviewData = await reviewApi.getByOrderId(orderId);
      return reviewData;
    } catch (error) {
      // 如果是404错误，说明没有评价，返回null
      if (error.statusCode === 404) {
        return null;
      }
      throw error;
    }
  },

  /**
   * 加载服务照片
   */
  async loadServicePhotos(orderId) {
    try {
      const servicePhotos = await orderApi.getServicePhotos(orderId);
      if (servicePhotos) {
        // 格式化时间
        const formattedPhotos = {
          ...servicePhotos,
          beforePhotoTime: servicePhotos.beforePhotoTime ? utils.formatNormalDate(servicePhotos.beforePhotoTime) : null,
          afterPhotoTime: servicePhotos.afterPhotoTime ? utils.formatNormalDate(servicePhotos.afterPhotoTime) : null,
        };
        this.setData({ servicePhotos: formattedPhotos });
      }
    } catch (error) {
      console.error('加载服务照片失败:', error);
      // 不显示错误提示，因为没有照片是正常情况
    }
  },

  /**
   * 预览服务照片
   */
  previewServicePhoto(e) {
    const { url, type } = e.currentTarget.dataset;
    const { servicePhotos } = this.data;

    if (!servicePhotos || !url || !type) return;

    // 根据类型获取对应的照片数组
    const photos = type === 'before' ? servicePhotos.beforePhotos : servicePhotos.afterPhotos;

    if (!photos || photos.length === 0) return;

    wx.previewImage({
      current: url,
      urls: photos
    });
  },

  /**
   * 预览评价图片
   */
  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    const { reviewData } = this.data;

    wx.previewImage({
      current: url,
      urls: reviewData.photoURLs || []
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: '查看我的服务评价',
      path: `/pages/reviewDetail/reviewDetail?orderId=${this.data.orderId}`,
      imageUrl: this.data.reviewData?.photoURLs?.[0] || ''
    };
  }
});
