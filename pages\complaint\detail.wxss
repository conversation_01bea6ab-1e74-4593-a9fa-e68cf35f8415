/* pages/complaint/detail.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 120rpx;
}

/* 状态卡片 */
.status-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  color: #fff;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: 600;
}

.category-tag {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background-color: rgba(255, 255, 255, 0.2);
}

.subcategory-text {
  font-size: 26rpx;
  opacity: 0.8;
}

.status-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.5;
}

/* 通用区块样式 */
.info-section,
.order-section,
.photo-section,
.result-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 信息项 */
.info-item {
  display: flex;
  margin-bottom: 24rpx;
  align-items: flex-start;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

.info-value.content {
  white-space: pre-wrap;
}

/* 订单卡片 */
.order-card {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.service-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.order-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.order-sn {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.order-price {
  font-size: 26rpx;
  color: #ff4f8f;
  font-weight: 600;
}

/* 图片容器 */
.photo-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.photo-item {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  border: 2rpx solid #f0f0f0;
}

/* 处理结果 */
.result-card {
  padding: 24rpx;
  background-color: #f0f9ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

.result-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  white-space: pre-wrap;
  display: block;
  margin-bottom: 16rpx;
}

.result-footer {
  display: flex;
  justify-content: flex-end;
}

.handle-time {
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮 */
.action-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.edit {
  background-color: #007aff;
  color: #fff;
}

.action-btn.edit:active {
  background-color: #0056cc;
}

.action-btn.delete {
  background-color: #ff3b30;
  color: #fff;
}

.action-btn.delete:active {
  background-color: #d70015;
}

/* 联系客服 */
.contact-section {
  margin-bottom: 20rpx;
}

.contact-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #ff4f8f 0%, #ff7ba7 100%);
  color: #fff;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(255, 79, 143, 0.3);
}

.contact-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 79, 143, 0.3);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 60rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #ff4f8f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
