.container {
  background-color: #f4f4f4;
  padding: 20rpx;
  font-family: Arial, sans-serif;
}

.order-header {
  text-align: center;
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.order-info {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.order-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
}

.product-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-service {
  background: rgba(255, 67, 145, 0.1);
  border-radius: 8px;
  padding: 8rpx 12rpx;
  width: 70%;
  line-height: 50rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.product-quantity {
  font-size: 24rpx;
  color: #999;
}

.paid-money {
  text-align: right;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.magin-bottom {
  margin-bottom: 20rpx;
}

.order-details {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  line-height: 60rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 200rpx;
}
.content{
  text-align: right;
}
.flex1-clz {
  border-top: 2rpx solid #e4e4e4;
  padding: 20rpx 32rpx 24rpx;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  bottom: 0rpx;
  background-color: #ffffff;
  box-shadow: 0rpx -2rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: visible;
  left: 0rpx;
}
.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16rpx;
}

.action-btn {
  background: rgba(238, 238, 238, 1);
  color: rgba(51, 51, 51, 1);
  font-weight: 500;
  min-width: 140rpx;
  text-align: center;
  white-space: nowrap;

  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  border: 1rpx solid;
  transition: all 0.3s ease;
}

.action-btn:nth-last-child(1) {
  background: rgba(47, 131, 255, 0.2);
  color: rgba(47, 131, 255, 1);
  border-radius: 40rpx;
}

/* 蓝色按钮样式 - 主要操作 */
.action-btn.blue-btn {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #ffffff;
  border: 1rpx solid #4A90E2;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.action-btn.blue-btn:active {
  background: linear-gradient(135deg, #357ABD 0%, #2E6BA8 100%);
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.4);
}

/* 灰色按钮样式 - 次要操作 */
.action-btn.gray-btn {
  background: #ffffff;
  color: #666666;
  border: 1rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.action-btn.gray-btn:active {
  background: #f8f8f8;
  border-color: #d0d0d0;
  transform: translateY(1rpx);
}
.more-btn {
  visibility: hidden;
  margin-right: 20rpx;
  font-size: 32rpx;
  position: relative;
  color: rgba(102, 102, 102, 1);
}
.more-actions-dropdown {
  position: absolute;
  bottom: 110rpx;
  left: 0;
  width: 260rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
}

.dropdown-item {
  padding: 20rpx;
  text-align: center;
  font-size: 24rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
  transition: background-color 0.3s;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f4f4f4;
}

/* 服务照片样式 */
.service-photos {
  margin-bottom: 20rpx;
}

.photo-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.photo-time {
  font-size: 24rpx;
  color: #999;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.photo-item {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.photo-preview {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}
