<!-- pages/complaint/list.wxml -->
<wxs module="utils">
function formatTime(timestamp) {
  var date = getDate(timestamp);
  var now = getDate();
  var diff = now.getTime() - date.getTime();

  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return Math.floor(diff / 60000) + '分钟前';
  } else if (diff < 86400000) { // 1天内
    return Math.floor(diff / 3600000) + '小时前';
  } else if (diff < 604800000) { // 1周内
    return Math.floor(diff / 86400000) + '天前';
  } else {
    return date.toLocaleDateString();
  }
}

module.exports.formatTime = formatTime;
</wxs>

<view class="container">
  <!-- 标签栏 -->
  <view class="tabs-container">
    <view class="tabs">
      <view 
        wx:for="{{tabs}}" 
        wx:key="key" 
        class="tab-item {{currentTab === item.key ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="{{item.key}}"
      >
        <text class="tab-text">{{item.label}}</text>
      </view>
    </view>
  </view>

  <!-- 投诉建议列表 -->
  <view class="complaint-list">
    <view 
      wx:for="{{complaintList}}" 
      wx:key="id" 
      class="complaint-item"
      bindtap="viewDetail"
      data-complaint="{{item}}"
    >
      <!-- 头部信息 -->
      <view class="complaint-header">
        <view class="category-info">
          <text class="category-tag {{item.category}}">{{categoryMap[item.category]}}</text>
          <text class="subcategory-text">{{subCategoryMap[item.subCategory]}}</text>
        </view>
        <view class="status-info">
          <text class="status-tag" style="color: {{statusMap[item.status].color}}">
            {{statusMap[item.status].text}}
          </text>
        </view>
      </view>

      <!-- 标题和内容 -->
      <view class="complaint-content">
        <text class="complaint-title">{{item.title}}</text>
        <text class="complaint-desc">{{item.content}}</text>
      </view>

      <!-- 关联订单信息 -->
      <view class="order-info" wx:if="{{item.orderId && item.orderInfo}}">
        <text class="order-label">关联订单：</text>
        <text class="order-sn">{{item.orderInfo.sn}}</text>
      </view>

      <!-- 底部信息 -->
      <view class="complaint-footer">
        <text class="create-time">{{utils.formatTime(item.createdAt)}}</text>
        <view class="actions" wx:if="{{item.status === 'pending'}}">
          <text class="action-btn edit" catchtap="editComplaint" data-complaint="{{item}}">编辑</text>
          <text class="action-btn delete" catchtap="deleteComplaint" data-complaint="{{item}}">删除</text>
        </view>
      </view>

      <!-- 处理结果 -->
      <view class="result-info" wx:if="{{item.result && item.status === 'resolved'}}">
        <text class="result-label">处理结果：</text>
        <text class="result-content">{{item.result}}</text>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{complaintList.length === 0 && !loading}}">
      <image class="empty-image" src="https://xian7.zos.ctyun.cn/pet/static/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无投诉建议记录</text>
      <button class="create-btn" bindtap="createComplaint">
        <text>提交投诉建议</text>
      </button>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && complaintList.length > 0}}">
      <text>没有更多了</text>
    </view>
  </view>

  <!-- 悬浮按钮 -->
  <view class="fab" bindtap="createComplaint">
    <text class="fab-icon">+</text>
  </view>
</view>
