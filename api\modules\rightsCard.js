import request, { analysisRes } from '../request';
import config from '../config';

const { rightsCard } = config.apiUrls;
export default {
  async list() {
    const res = await request.get(rightsCard.list);
    return analysisRes(res);
  },

  /**
   * 购买权益卡
   * @param {string} customerId 客户ID
   * @param {string} cardTypeId 权益卡ID
   * @param {string} remark 说明说明文字
   * @returns {Promise<any>} 返回订单信息，包含sn等
   */
  async buy(customerId, cardTypeId, remark) {
    const res = await request.post(rightsCard.buy, {
      customerId,
      cardTypeId,
      remark,
    });
    return analysisRes(res);
  },

  /**
   * 完成支付
   * @param {string} sn 订单号
   * @param {string} cardTypeId 权益卡ID
   * @returns {Promise<any>} 返回订单信息，包含sn等
   */
  async pay(sn, customerId) {
    const res = await request.post(rightsCard.pay.replace(':sn', sn), { customerId });
    return analysisRes(res);
  },

  /**
   * 获取我的权益卡列表
   * @param {string} customerId 客户ID
   * @returns {Promise<any>} 返回权益卡列表
   */
  async myCards(customerId) {
    const res = await request.get(rightsCard.myCards.replace('{customerId}', customerId));
    return analysisRes(res);
  },

  /**
   * 获取我的有效权益卡列表
   * @param {string} customerId 客户ID
   * @returns {Promise<any>} 返回权益卡列表
   */
  async myValidCards(customerId) {
    const res = await request.get(rightsCard.myValidCards.replace('{customerId}', customerId));
    return analysisRes(res);
  },

  /**
   * 获取服务可用的权益卡列表
   * @param {string} customerId 客户ID
   * @param {string} serviceId 服务ID
   * @param {number} amount 订单金额
   * @returns {Promise<any>} 返回可用权益卡列表
   */
  async getAvailableCards(customerId, serviceId, amount) {
    const res = await request.get(rightsCard.availableCards.replace('{customerId}', customerId), { serviceId, amount });
    return analysisRes(res);
  },
};
