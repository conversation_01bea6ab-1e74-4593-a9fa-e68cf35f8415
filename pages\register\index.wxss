.register-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 30rpx;
}

/* 头像区域 */
.avatar-section {
  text-align: center;
  padding: 40rpx 0;
}

.avatar-wrapper {
  margin: 0 auto;
  padding: 0;
  width: 176rpx;
  height: 176rpx;
  border-radius: 50%;
  background: none;
}

.avatar-wrapper::after {
  border: none;
}

.avatar {
  width: 176rpx;
  height: 176rpx;
  border-radius: 50%;
}

.tips {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 授权按钮区域 */
.auth-section {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.auth-btn,
.custom-btn {
  width: 220rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
}

.auth-btn {
  background: rgba(255, 67, 145, 1);
  color: #fff;
}

.custom-btn {
  background: #f7f8fa;
  color: #333;
}

/* 表单区域 */
.form-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #eee;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
}

.required::before {
  content: '*';
  color: #ee0a24;
  margin-right: 4rpx;
}

.input {
  flex: 1;
  height: 100rpx;
  font-size: 28rpx;
}

/* 单选框组 */
.radio-group {
  flex: 1;
  display: flex;
  gap: 40rpx;
}

.radio-label {
  font-size: 28rpx;
  color: #333;
}

/* 地址选择器 */
.address-picker {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 100rpx;
}

.address-text {
  font-size: 28rpx;
  color: #999;
}

.arrow {
  color: #999;
  font-size: 28rpx;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  margin-top: 40rpx;
  background: rgba(255, 67, 145, 1);
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
}

/* 地址选择器弹窗 */
.picker-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  visibility: hidden;
  z-index: 999;
}

.picker-popup.show {
  visibility: visible;
}

.picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.picker-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.picker-popup.show .picker-content {
  transform: translateY(0);
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-header .title {
  font-size: 32rpx;
  color: #333;
}

.picker-header .cancel,
.picker-header .confirm {
  font-size: 28rpx;
  color: #666;
}

.picker-header .confirm {
  color: rgba(255, 67, 145, 1);
}

.region-picker {
  width: 100%;
  height: 480rpx;
}

.tip {
  line-height: 50rpx;
  font-size: 22rpx;
  margin-top: 20rpx;
}

.tip .icon3 {
  margin-right: 10rpx;
  color: rgba(47, 131, 255, 1);
}

.tip .text-link {
  color: rgba(47, 131, 255, 1);
}

.getPhoneNumber {
  background-color: transparent;
  padding: 0;
  font-size: 30rpx;
  color: rgba(47, 131, 255, 1);
}

.getPhoneNumber::after {
  border: none;
}

/* 推广码相关样式 */
.promotion-code-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.checking-status {
  font-size: 24rpx;
  color: #999;
}

.code-status {
  font-size: 24rpx;
}

.code-status .success {
  color: #07c160;
}

.code-status .error {
  color: #ee0a24;
}