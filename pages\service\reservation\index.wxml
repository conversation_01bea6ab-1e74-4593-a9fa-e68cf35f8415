<!-- pages/service/reservation/index.wxml -->
<wxs module="utils">
  function formatNumber(num) {
    return parseFloat(num).toFixed(2);
  }
  module.exports = {
    formatNumber: formatNumber
  };
</wxs>

<view class="reservationPage">
  <view class="flex-card">
    <view class="flex diygw-col-24 items-stretch flex-nowrap">
      <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center">
        <image src="{{serviceInfo.logo}}" class="image-size diygw-image diygw-col-0" mode="widthFix"></image>
      </view>
      <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-between right-content">
        <view>
          <text class="diygw-col-0 text-bold">{{serviceInfo.serviceName}}</text>
          <view class="flex flex-wrap diygw-col-24 text-grey">{{serviceInfo.weightDescription}}</view>
        </view>
        <view class="service-num">服务单价 ￥{{serviceInfo.basePrice}}</view>
        <!-- <view class="service-num">累计服务{{serviceInfo.count}}次</view> -->
      </view>
    </view>
    <view class="clearfix"></view>
  </view>
  <view slot="content" class="service-navbar">
    <view class="flex tabs diygw-col-24 flex-direction-column">
      <view class="diygw-tabs text-center scale-title small-border tabs-title">
        <view class="diygw-tab-item tabs-item-title  {{index==tabsIndex?'cur':''}}" wx:for="{{tabsDatas}}" wx:key="index" wx:for-item="item" wx:for-index="index" data-key="index" data-index="{{index}}" catchtap="changeTabs">
          {{item.text}}
        </view>
      </view>
    </view>
    <view class="clearfix"></view>
    <view class="tab-container">
      <view wx:if="{{tabsIndex==0}}">
        <view class="type-group" wx:for="{{addService}}" wx:for-item="service" wx:key="id" wx:for-index="index" data-key="index" data-index="{{index}}">
          <view class="type-name">{{service.name}}</view>
          <view class="flex flex-wrap diygw-col-24 items-stretch type-group-children">
            <view class="diygw-col-6 flex-direction-column items-center type-sub-group {{item.isAdd==='add'?'active':''}}" wx:for="{{service.children}}" wx:key="id" wx:for-item="item" wx:for-index="index" data-key="index" data-index="{{index}}" data-price="{{item.price}}" data-id="{{item.id}}" data-pid="{{service.id}}" bind:tap="addService">
              <view class="flex flex-wrap diygw-col-24 flex-direction-column items-center">
                <image src="{{item.logo}}" />
                <view>
                  <view class="type-sub-name">{{item.name}}</view>
                  <view class="type-sub-price">￥{{item.price}}</view>
                  <view class="type-sub-desc">{{item.desc}}</view>
                </view>
              </view>
              <image wx:if="{{item.isAdd==='add'}}" class="diy-icon-min" src='https://xian7.zos.ctyun.cn/pet/static/minus.png' />
              <image wx:else class="diy-icon-min" src='https://xian7.zos.ctyun.cn/pet/static/roundAdd.png' />
            </view>
          </view>
          <view class="clearfix"></view>
        </view>
      </view>
      <view wx:if="{{tabsIndex== 1}}" class="autoHeigth">
        <image src="{{serviceIntro}}" class="serviceIntroduction" mode="widthFix" />
      </view>
      <view wx:if="{{tabsIndex== 2}}">
        <appointment-notice />
      </view>
      <view wx:if="{{tabsIndex== 3}}">
        <empty icon="//xian7.zos.ctyun.cn/pet/static/nopj.png" text="暂无评价" class="empty" />
      </view>
    </view>
  </view>
  <view id="bottom" class="flex flex-wrap diygw-col-24 justify-between items-center diygw-bottom shop-bar">
    <image src="//xian7.zos.ctyun.cn/pet/static/shop.png" class="image3-size diygw-image diygw-col-0 image3-clz" mode="widthFix"></image>
    <view class="flex flex-wrap diygw-col-16 justify-between items-center flex8-clz">
      <view class="flex flex-wrap diygw-col-0 items-baseline justify-end flex12-clz">
        <text class="diygw-col-0"> 合计：</text>
        <text class="diygw-col-0 color-pink"> ￥<text class="money-num">{{shop.totalMoney}}</text> </text>
      </view>
      <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center flex13-clz" data-open="open" bind:tap="pleaseClick">
        <view class="flex flex-wrap diygw-col-0 flex-direction-column items-center">
          <text class="diygw-col-0"> 下一步 </text>
        </view>
      </view>
    </view>
  </view>
  <view wx:if="{{modal.isShow}}" class="container container336456">
    <view class="diygw-modal bottom-modal show" style="z-index: 100" data-open="clode" bind:tap="pleaseClick">
      <view class="diygw-dialog diygw-dialog-modal" catch:tap="preventClick">
        <view class="flex diygw-dialog-content">
          <view class="flex flex-wrap diygw-col-24 flex-direction-column flex-card">
            <view class="flex flex-wrap diygw-col-24  items-center flex-card-title">
              <image class="diygw-col-0" src='https://xian7.zos.ctyun.cn/pet/static/pet1.png' />
              <text class="diygw-col-0">服务宠物</text>
            </view>
            <view class="flex flex-wrap diygw-col-24 items-baseline flex6-clz">
              <view wx:if="{{petInfo}}" class="flex diygw-col-0 items-center justify-between flex-nowrap flex46-clz" style="background-color: {{petInfo.gender===1 ? 'rgba(122, 221, 252, 0.3)':petInfo.gender===0 ?'rgba(255, 251, 220, 1)':''}};">
                <image src="{{petInfo.avatar||siteinfo.constant.pet[petInfo.type]}}" class="image7-size diygw-image diygw-col-0" mode="widthFix"></image>
                <text class="diygw-col-0 text23-clz"> {{petInfo.name}} </text>
              </view>
              <button wx:else class="color-pink pink-btn"><text class="diy-icon-add"></text>添加宠物</button>
            </view>
          </view>
          <view class="flex flex-wrap diygw-col-24 flex-direction-column flex-card">
            <view class="flex flex-wrap diygw-col-24  items-center flex-card-title">
              <image class="diygw-col-0" src='https://xian7.zos.ctyun.cn/pet/static/address.png' />
              <view wx:if="{{adressInfo}}" class="flex align-center tagBox" data-type='adress' bind:tap="redirect">
                <image class="systemDispatch" src="//xian7.zos.ctyun.cn/pet/static/exchange.png" />
                <text class="describe">更换地址</text>
              </view>
              <text class="diygw-col-0">服务地址</text>
            </view>
            <view wx:if="{{adressInfo}}" class="flex flex-wrap diygw-col-24 flex-direction-column flex-address">
              <text class="diygw-text-line2 diygw-col-24 text-adress"> {{adressInfo.detailAddress}}</text>
              <view class="flex flex-wrap diygw-col-24 justify-between adressInfos-center">
                <view>
                  <text wx:if="{{adressInfo.contactName}}" class="diygw-text-line3 diygw-col-0 text-subtip"> {{adressInfo.contactName}} </text>
                  <text wx:if="{{adressInfo.contactPhone}}" class="diygw-text-line3 diygw-col-0 text-subtip"> {{adressInfo.contactPhone}} </text>
                </view>
              </view>
            </view>
            <view wx:else class="flex flex-wrap diygw-col-24 items-center justify-center flex6-clz" data-type='adress' bind:tap="redirect">
              <button class="color-pink pink-btn"><text class="diy-icon-add"></text>添加地址</button>
            </view>
          </view>
          <view class="flex flex-wrap diygw-col-24 flex-direction-column flex-card">
            <view class="flex flex-wrap diygw-col-24  items-center flex-card-title">
              <image class="diygw-col-0" src='https://xian7.zos.ctyun.cn/pet/static/date.png' />
              <!-- <view wx:if="{{expectTime}}" class="flex align-center tagBox">
                <image class="systemDispatch" src="//xian7.zos.ctyun.cn/pet/static/exchange.png" />
                <text class="describe">更换时间</text>
              </view> -->
              <text class="diygw-col-0">期望上门时间</text>
            </view>
            <view class="flex flex-wrap diygw-col-24 items-baseline justify-center flex6-clz">
              <custom-picker clickEvent="{{clickTimeEvent}}" />
            </view>
          </view>

          <!-- 订单金额 -->
          <view class="flex flex-wrap diygw-col-24 flex-direction-column flex-card">
            <view class="flex flex-wrap diygw-col-24 items-center flex-card-title">
              <image class="diygw-col-0" src='https://xian7.zos.ctyun.cn/pet/static/money.png' />
              <text class="diygw-col-0">订单金额</text>
            </view>
            <view class="flex flex-wrap diygw-col-24 items-center justify-between padding-sm">
              <text class="diygw-col-0">服务费用</text>
              <text class="diygw-col-0">￥{{utils.formatNumber(serviceInfo.basePrice)}}</text>
            </view>
            <view wx:if="{{shop.totalNumber > 0}}" class="flex flex-wrap diygw-col-24 flex-direction-column padding-sm">
              <view class="flex flex-wrap diygw-col-24 items-center justify-between">
                <text class="diygw-col-0">增项服务</text>
                <text class="diygw-col-0">￥{{utils.formatNumber(additionalServiceFee)}}</text>
              </view>
              <!-- 增项服务列表 -->
              <view class="additional-services-list">
                <view wx:for="{{selectedServices}}" wx:key="id" class="flex flex-wrap diygw-col-24 items-center justify-between padding-xs">
                  <text class="diygw-col-0 text-sm text-grey">· {{item.name}}</text>
                  <text class="diygw-col-0 text-sm text-grey">￥{{utils.formatNumber(item.price)}}</text>
                </view>
              </view>
            </view>
            <!-- 折扣信息 -->
            <view wx:if="{{bestDiscountCard}}" class="flex flex-wrap diygw-col-24 items-center justify-between padding-sm">
              <text class="diygw-col-0">折扣卡优惠</text>
              <text class="diygw-col-0 color-green">-￥{{utils.formatNumber(discountAmount)}}</text>
            </view>

            <!-- 优惠券/次卡信息 -->
            <view wx:if="{{discountType === 'coupon' && selectedCoupon}}" class="flex flex-wrap diygw-col-24 items-center justify-between padding-sm">
              <text class="diygw-col-0">优惠券</text>
              <text class="diygw-col-0 color-green">-￥{{utils.formatNumber(couponAmount)}}</text>
            </view>
            <view wx:if="{{discountType === 'times' && selectedTimesCard}}" class="flex flex-wrap diygw-col-24 items-center justify-between padding-sm">
              <text class="diygw-col-0">次卡抵扣</text>
              <text class="diygw-col-0 color-green">-￥{{utils.formatNumber(couponAmount)}}</text>
            </view>

            <!-- 选择优惠券/次卡 -->
            <view class="discount-selector-row" bindtap="showDiscountSelector">
              <view class="discount-selector-left">
                <view class="discount-selector-icon">
                  <text class="coupon-icon-text">券</text>
                </view>
                <view class="discount-selector-content">
                  <text class="discount-selector-title">优惠选择</text>
                  <view wx:if="{{availableCoupons.length > 0 || timesCards.length > 0}}" class="discount-badge-enhanced">
                    <text wx:if="{{availableCoupons.length > 0}}">{{availableCoupons.length}}张券</text>
                    <text wx:if="{{availableCoupons.length > 0 && timesCards.length > 0}}">、</text>
                    <text wx:if="{{timesCards.length > 0}}">{{timesCards.length}}张卡</text>
                    <text>可用</text>
                  </view>
                  <view wx:else class="discount-no-available">
                    <text>暂无可用优惠</text>
                  </view>
                </view>
              </view>
              <view class="discount-selector-right">
                <text class="discount-status" wx:if="{{discountType === 'none'}}">未使用</text>
                <text class="discount-status selected" wx:if="{{discountType === 'coupon'}}">已选择优惠券</text>
                <text class="discount-status selected" wx:if="{{discountType === 'times'}}">已选择次卡</text>
                <image class="arrow-right-enhanced" src='https://xian7.zos.ctyun.cn/pet/static/arrow-right.png' />
              </view>
            </view>

            <view class="flex flex-wrap diygw-col-24 items-center justify-between padding-sm" style="border-top: 1rpx solid #eee; padding-top: 20rpx;">
              <text class="diygw-col-0 text-bold">合计</text>
              <text class="diygw-col-0 color-pink text-bold">￥{{utils.formatNumber(finalPrice)}}</text>
            </view>
          </view>
          <view wx:if="{{showService}}" class="flex flex-wrap diygw-col-24 flex-direction-column flex-card">
            <view class="flex flex-wrap diygw-col-24  items-center flex-card-title">
              <image class="diygw-col-0" src='https://xian7.zos.ctyun.cn/pet/static/person.png' />
              <text class="diygw-col-0">服务人员</text>
              <view class="flex align-center tagBox">
                <image class="systemDispatch" src="//xian7.zos.ctyun.cn/pet/static/default.png" />
                <text class="describe">系统派单</text>
              </view>
            </view>
            <view wx:if="{{people}}" class="flex flex-wrap diygw-col-16 items-center flex-person">
              <image class="exchangeIcon" src='https://xian7.zos.ctyun.cn/pet/static/exchange-b.png' />
              <image src="{{people.avatar}}" class="response diygw-col-8 avatar" mode="aspectFill"></image>
              <view class="flex flex-wrap diygw-col-16 flex-direction-column items-stretch">
                <text class="diygw-col-24 list-title"> {{people.name}} </text>
                <view class="diygw-form-item diygw-col-24 " style="padding: 10rpx 32rpx !important;">
                  <view class="input" style="overflow-x: initial">
                    <text wx:for="{{[1,2,3,4,5]}}" wx:for-item="rateitem" wx:for-index="index" class="diy-icon-starfill pointer" data-index="{{rateitem}}" style="color:{{rateitem<=people.rate?'rgba(245, 78, 6, 1)':'#eee'}};font-size:12px" catchtap="changeRate">
                      <input type="hidden" hidden name="rate" value="{{people.rate}}" />
                    </text>
                    {{people.rate}}
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="flex flex-wrap diygw-col-24 justify-between items-center shop-bar margin-top">
            <view class="flex flex-wrap diygw-col-24 justify-center items-center flex13-clz" bind:tap="payOrder">
              <text class="diygw-col-0"> 去结算 </text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="clearfix"></view>
  </view>
  <custom-modal
    show="{{showModal}}"
    title="{{modalTitle}}"
    content="{{modalContent}}"
    buttons="{{modalButtons}}"
    bind:confirm="handleModalConfirm"
    bind:cancel="handleModalCancel"
    bind:modalConfirm="handleModalConfirm"
    bind:modalCancel="handleModalCancel"
  ></custom-modal>

  <!-- 使用新的优惠选择器组件 -->
  <discount-selector
    show="{{showDiscountSelector}}"
    originalPrice="{{originalPrice}}"
    discountedPrice="{{discountedPrice}}"
    bestDiscountCard="{{bestDiscountCard}}"
    availableCoupons="{{availableCoupons}}"
    timesCards="{{timesCards}}"
    discountType="{{discountType}}"
    selectedCoupon="{{selectedCoupon}}"
    selectedTimesCard="{{selectedTimesCard}}"
    bind:confirm="onDiscountConfirm"
    bind:close="hideDiscountSelector">
  </discount-selector>
</view>