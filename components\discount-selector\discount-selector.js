// components/discount-selector/discount-selector.js

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    show: {
      type: Boolean,
      value: false,
    },
    // 原始价格
    originalPrice: {
      type: Number,
      value: 0,
    },
    // 折扣后价格（应用折扣卡后）
    discountedPrice: {
      type: Number,
      value: 0,
    },
    // 最佳折扣卡
    bestDiscountCard: {
      type: Object,
      value: null,
    },
    // 可用的优惠券列表
    availableCoupons: {
      type: Array,
      value: [],
    },
    // 可用的次卡列表
    timesCards: {
      type: Array,
      value: [],
    },
    // 当前折扣类型
    discountType: {
      type: String,
      value: 'none',
    },
    // 选中的优惠券
    selectedCoupon: {
      type: Object,
      value: null,
    },
    // 选中的次卡
    selectedTimesCard: {
      type: Object,
      value: null,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 当前选中的标签
    currentTab: 'none',
    // 选中的优惠券ID
    selectedCouponId: '',
    // 选中的次卡ID
    selectedTimesCardId: '',
    // 预览价格
    previewPrice: 0,
    // 当前预览的折扣类型
    currentPreviewDiscountType: 'none',
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    init() {
      const { discountType, selectedCoupon, selectedTimesCard, availableCoupons, timesCards } = this.properties;

      // 设置初始选中的标签
      let initialTab = 'none';
      if (discountType === 'times') {
        initialTab = 'times';
      } else if (discountType === 'coupon') {
        initialTab = 'coupon';
      } else if (timesCards && timesCards.length > 0) {
        initialTab = 'times';
      } else if (availableCoupons && availableCoupons.length > 0) {
        initialTab = 'coupon';
      }

      this.setData({
        currentTab: initialTab,
        selectedCouponId: selectedCoupon ? selectedCoupon.id : '',
        selectedTimesCardId: selectedTimesCard ? selectedTimesCard.id : '',
        previewPrice: this.properties.discountedPrice,
        currentPreviewDiscountType: discountType,
      });

      // 初始化预览价格
      this.previewDiscountPrice(initialTab);
    },

    /**
     * 切换标签
     */
    switchTab(e) {
      const tab = e.currentTarget.dataset.tab;
      console.log('切换标签', tab);

      this.setData({
        currentTab: tab,
        currentPreviewDiscountType: tab,
      });

      // 切换标签时更新预览价格
      this.previewDiscountPrice(tab);

      // 如果切换到"不使用"标签，清空选择
      if (tab === 'none') {
        this.setData({
          selectedCouponId: '',
          selectedTimesCardId: '',
          previewPrice: this.properties.discountedPrice,
        });
      }
    },

    /**
     * 选择优惠券
     */
    selectCoupon(e) {
      const id = e.currentTarget.dataset.id;
      const { selectedCouponId } = this.data;

      console.log('选择优惠券', id);

      // 如果已经选中了这个优惠券，再次点击取消选中
      if (selectedCouponId === id) {
        this.setData({
          selectedCouponId: '',
          currentPreviewDiscountType: 'none',
        });
      } else {
        this.setData({
          selectedCouponId: id,
          currentPreviewDiscountType: 'coupon',
        });
      }

      // 立即计算并显示优惠后的金额
      this.previewDiscountPrice(this.data.currentPreviewDiscountType);
    },

    /**
     * 选择次卡
     */
    selectTimesCard(e) {
      const id = e.currentTarget.dataset.id;
      const { selectedTimesCardId } = this.data;

      console.log('选择次卡', id);

      // 如果已经选中了这个次卡，再次点击取消选中
      if (selectedTimesCardId === id) {
        this.setData({
          selectedTimesCardId: '',
          currentPreviewDiscountType: 'none',
        });
      } else {
        this.setData({
          selectedTimesCardId: id,
          currentPreviewDiscountType: 'times',
        });
      }

      // 立即计算并显示优惠后的金额
      this.previewDiscountPrice(this.data.currentPreviewDiscountType);
    },

    /**
     * 计算折扣卡折扣后的价格
     */
    calculateDiscountCardPrice(originalPrice, discountCard) {
      let discountedPrice = originalPrice;

      if (discountCard) {
        const discountRate = Number(discountCard.discountRate) || 1;
        discountedPrice = (originalPrice * discountRate).toFixed(2);
      }

      return discountedPrice;
    },

    /**
     * 预览优惠后的价格
     */
    previewDiscountPrice(discountType) {
      const { originalPrice, bestDiscountCard, availableCoupons, timesCards } = this.properties;
      const { selectedCouponId, selectedTimesCardId } = this.data;

      console.log('预览价格计算开始', {
        discountType,
        originalPrice,
        selectedCouponId,
        selectedTimesCardId,
      });

      // 首先应用折扣卡
      const discountedPrice = this.calculateDiscountCardPrice(originalPrice, bestDiscountCard);

      // 然后根据折扣类型应用优惠券或次卡
      let previewFinalPrice = discountedPrice;

      if (discountType === 'times' && selectedTimesCardId) {
        // 使用次卡
        previewFinalPrice = 0;
      } else if (discountType === 'coupon' && selectedCouponId) {
        // 使用优惠券
        const selectedCoupon = availableCoupons.find(item => item.id === selectedCouponId);
        if (selectedCoupon) {
          // 获取优惠券金额，尝试多个可能的字段名
          const deduction = Number(selectedCoupon.amount || selectedCoupon.value || selectedCoupon.deduction || 0);
          previewFinalPrice = Math.max(0, discountedPrice - deduction).toFixed(2);
        }
      }

      console.log('预览价格计算结果', {
        previewFinalPrice,
        discountedPrice,
      });

      // 更新预览价格
      this.setData({
        previewPrice: previewFinalPrice,
        currentPreviewDiscountType: discountType,
      });
    },

    /**
     * 查找优惠券
     */
    findCoupon(couponId, availableCoupons) {
      if (!couponId || !availableCoupons || availableCoupons.length === 0) {
        return null;
      }
      return availableCoupons.find(item => item.id === couponId);
    },

    /**
     * 查找次卡
     */
    findTimesCard(timesCardId, timesCards) {
      if (!timesCardId || !timesCards || timesCards.length === 0) {
        return null;
      }
      return timesCards.find(item => item.id === timesCardId);
    },

    /**
     * 确定折扣类型和选中的优惠
     */
    determineDiscount(params) {
      const { currentDiscountType, selectedCouponId, selectedTimesCardId, availableCoupons, timesCards } = params;

      let discountType = currentDiscountType || 'none';
      let selectedCoupon = null;
      let selectedTimesCard = null;

      if (discountType === 'coupon' && selectedCouponId) {
        selectedCoupon = this.findCoupon(selectedCouponId, availableCoupons);
        if (!selectedCoupon) {
          discountType = 'none';
        }
      } else if (discountType === 'times' && selectedTimesCardId) {
        selectedTimesCard = this.findTimesCard(selectedTimesCardId, timesCards);
        if (!selectedTimesCard) {
          discountType = 'none';
        }
      } else {
        discountType = 'none';
      }

      return {
        discountType,
        selectedCoupon,
        selectedTimesCard,
      };
    },

    /**
     * 确认选择
     */
    confirmDiscount() {
      const { selectedCouponId, selectedTimesCardId, currentPreviewDiscountType } = this.data;
      const { availableCoupons, timesCards } = this.properties;

      console.log('确认选择优惠', {
        currentPreviewDiscountType,
        selectedCouponId,
        selectedTimesCardId,
      });

      // 确定折扣类型和选中的优惠
      const result = this.determineDiscount({
        currentDiscountType: currentPreviewDiscountType,
        selectedCouponId,
        selectedTimesCardId,
        availableCoupons,
        timesCards,
      });

      console.log('确定的折扣类型和选中的优惠', result);

      // 触发确认事件
      this.triggerEvent('confirm', {
        discountType: result.discountType,
        selectedCoupon: result.selectedCoupon,
        selectedTimesCard: result.selectedTimesCard,
        previewPrice: this.data.previewPrice,
      });
    },

    /**
     * 关闭选择器
     */
    close() {
      this.triggerEvent('close');
    },

    /**
     * 阻止滑动穿透
     */
    preventTouchMove() {
      return false;
    },
  },

  /**
   * 组件生命周期函数
   */
  lifetimes: {
    attached() {
      // 在组件实例进入页面节点树时执行
      this.init();
    },
  },

  /**
   * 组件所在页面的生命周期函数
   */
  pageLifetimes: {
    show() {
      // 页面被展示时执行
      console.log('页面被展示', this.properties.show);
      if (this.properties.show) {
        this.init();
      }
    },
  },

  /**
   * 监听属性变化
   */
  observers: {
    show: function (show) {
      console.log('show 属性变化', show);
      if (show) {
        this.init();
      }
    },
  },
});
