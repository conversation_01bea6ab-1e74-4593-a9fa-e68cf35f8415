.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 状态卡片样式 */
.status-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  color: #fff;
}

.status-icon {
  font-size: 60rpx;
  margin-right: 30rpx;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.status-text {
  font-size: 36rpx;
  font-weight: bold;
}

.status-desc {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 信息区块样式 */
.info-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 信息列表样式 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
}

.info-value.link {
  color: #007aff;
  text-decoration: underline;
}

/* 服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.service-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.service-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.service-spec {
  font-size: 24rpx;
  color: #666;
}

.service-total {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 价格明细样式 */
.price-details {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}

.price-row:not(:last-child) {
  border-bottom: 1rpx solid #f0f0f0;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.price-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.price-row.discount .price-value {
  color: #ff6b35;
}

.price-row.total {
  padding-top: 20rpx;
  border-top: 2rpx solid #e5e5e5;
  margin-top: 10rpx;
}

.price-row.total .price-label {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.total-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 优惠信息样式 */
.discount-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.discount-item {
  padding: 20rpx;
  background-color: #f0f8ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

.discount-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.discount-type {
  font-size: 28rpx;
  color: #007aff;
  font-weight: bold;
}

.discount-amount {
  font-size: 26rpx;
  color: #666;
}

/* 客户信息样式 */
.customer-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 备注信息样式 */
.remark-content {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #28a745;
}

.remark-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 操作按钮样式 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background-color: #007aff;
  color: #fff;
}

.action-btn.secondary {
  background-color: #f0f0f0;
  color: #666;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
