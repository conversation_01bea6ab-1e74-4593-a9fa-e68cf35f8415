<view class="container">
  <view class="order-info">
    <view class="order-content">
      <image class="product-image" src="{{orderDetail.orderDetails[0].service.logo}}"></image>
      <view class="product-info">
        <view class="flex align-center justify-between">
          <text class="product-name">{{orderDetail.orderDetails[0].service.serviceName}}</text>
          <text class="product-price">¥ <text class="paid-money">{{orderDetail.totalFee}}</text></text>
        </view>
        <view class="flex align-center justify-between">
          <text wx:if="{{!!orderDetail.orderDetails[0].additionalServices.length}}" class="product-service">增项服务：<text wx:for="{{orderDetail.orderDetails[0].additionalServices}}" wx:for-item="val" wx:key="val.id">{{val.name}};</text></text>
          <text wx:else class="product-service">增项服务：无</text>
          <!-- <text class="product-quantity">x 1</text> -->
        </view>
      </view>
    </view>

    <view class="order-details">
      <view class="detail-item" wx:if="{{orderDetail.originalPrice}}">
        <text class="label">原价</text>
        <text class="total-price">¥ <text class="paid-money">{{orderDetail.originalPrice}}</text></text>
      </view>
      <view class="detail-item">
        <text class="label">服务总价</text>
        <text class="total-price">¥ <text class="paid-money">{{orderDetail.totalFee}}</text></text>
      </view>
      <view class="detail-item">
        <text class="label">实付款</text>
        <text class="total-price">¥ <text class="paid-money">{{orderDetail.totalFee}}</text></text>
      </view>
      <view class="detail-item">
        <text class="label">订单编号</text>
        <text class="content">{{orderDetail.sn}}</text>
      </view>
      <view class="detail-item">
        <text class="label">服务宠物</text>
        <text class="content">{{orderDetail.orderDetails[0].petName}}</text>
      </view>
      <view class="detail-item">
        <text class="label">服务人员</text>
        <text class="content">{{orderDetail.employee.name}}</text>
      </view>
      <view class="detail-item">
        <text class="label">期望上门时间</text>
        <text class="content">{{orderDetail.serviceTime || '待预约'}}</text>
      </view>
      <view class="detail-item">
        <text class="label">服务地址</text>
        <text class="content">{{orderDetail.address}}</text>
      </view>
      <view class="detail-item">
        <text class="label">下单时间</text>
        <text class="content">{{orderDetail.orderTime}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">付款时间</text>
        <text class="content">{{orderDetail.payAt}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">服务时间</text>
        <text class="content">{{orderDetail.serviceAt}}</text>
      </view>
      <view class="detail-item hidden">
        <text class="label">完成时间</text>
        <text class="content">{{orderDetail.dealAt}}</text>
      </view>
    </view>
  </view>

  <!-- 服务照片区域 -->
  <view wx:if="{{servicePhotos && ((servicePhotos.beforePhotos && servicePhotos.beforePhotos.length > 0) || (servicePhotos.afterPhotos && servicePhotos.afterPhotos.length > 0))}}" class="service-photos">
    <!-- 服务前照片 -->
    <view wx:if="{{servicePhotos.beforePhotos && servicePhotos.beforePhotos.length > 0}}" class="photo-section">
      <view class="section-title">
        <text class="title-text">服务前照片</text>
        <text class="photo-time" wx:if="{{servicePhotos.beforePhotoTime}}">{{servicePhotos.beforePhotoTime}}</text>
      </view>
      <view class="photo-grid">
        <view wx:for="{{servicePhotos.beforePhotos}}" wx:key="index" class="photo-item">
          <image
            src="{{item}}"
            class="photo-preview"
            mode="aspectFill"
            bindtap="previewServicePhoto"
            data-url="{{item}}"
            data-type="before"
          ></image>
        </view>
      </view>
    </view>

    <!-- 服务后照片 -->
    <view wx:if="{{servicePhotos.afterPhotos && servicePhotos.afterPhotos.length > 0}}" class="photo-section">
      <view class="section-title">
        <text class="title-text">服务后照片</text>
        <text class="photo-time" wx:if="{{servicePhotos.afterPhotoTime}}">{{servicePhotos.afterPhotoTime}}</text>
      </view>
      <view class="photo-grid">
        <view wx:for="{{servicePhotos.afterPhotos}}" wx:key="index" class="photo-item">
          <image
            src="{{item}}"
            class="photo-preview"
            mode="aspectFill"
            bindtap="previewServicePhoto"
            data-url="{{item}}"
            data-type="after"
          ></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 更多操作弹窗 -->
  <view wx:if="{{showMoreActions}}" class="more-actions-dropdown">
    <view class="dropdown-item" bindtap="viewOrderDetail" data-order-id="{{item.orderId}}">
      更改服务地址
    </view>
    <view class="dropdown-item" bindtap="deleteOrder" data-order-id="{{item.orderId}}">
      更换服务人员
    </view>
    <view class="dropdown-item" bindtap="toggleOrderActions" data-order-id="{{item.orderId}}">
      取消订单
    </view>
  </view>
  <view class="diygw-col-24 diygw-bottom flex1-clz">
    <view class="flex align-center justify-between">
      <view class="more-btn" bindtap="toggleOrderActions" data-order-id="{{orderDetail.orderId}}">更多</view>
      <view class='order-actions'>
        <block wx:if="{{orderDetail.status === '待付款'}}">
          <view class="action-btn" bindtap="cancelOrder" data-order-id="{{orderDetail.id}}">
            取消订单
          </view>
          <view class="action-btn" bindtap="payOrder" data-sn="{{orderDetail.sn}}">
            去付款
          </view>
        </block>

        <block wx:if="{{orderDetail.status === 'paid'}}">
          <view class="action-btn blue-btn" bindtap="confirmReceipt" data-order-id="{{orderDetail.orderId}}">
            催接单
          </view>
        </block>

        <block wx:if="{{orderDetail.status === '服务中'}}">
          <view class="action-btn blue-btn" bindtap="applyAdditionalService" data-order-detail-id="{{orderDetail.id}}">
            申请追加服务
          </view>
          <view class="action-btn gray-btn" bindtap="viewAdditionalServices" data-order-detail-id="{{orderDetail.id}}">
            查看追加服务
          </view>
        </block>

        <block wx:if="{{orderDetail.status === 'completed'}}">
          <view class="action-btn blue-btn" bindtap="reviewOrder" data-order-id="{{orderDetail.orderId}}">
            去评价
          </view>
        </block>
      </view>
    </view>
  </view>
  <custom-modal
    show="{{showModal}}"
    title="{{modalTitle}}"
    content="{{modalContent}}"
    buttons="{{modalButtons}}"
    bind:confirm="handleModalConfirm"
    bind:cancel="handleModalCancel"
    bind:modalConfirm="handleModalConfirm"
    bind:modalCancel="handleModalCancel"
  ></custom-modal>
</view>