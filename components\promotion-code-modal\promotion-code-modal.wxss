.promotion-code-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  width: 80%;
  max-width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-content {
  padding: 30rpx;
}

.promotion-tip {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}

.promotion-input-wrapper {
  margin-bottom: 20rpx;
}

.promotion-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.promotion-input:focus {
  border-color: #667eea;
}

.checking-status {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  text-align: center;
}

.code-status {
  font-size: 24rpx;
  margin-top: 10rpx;
  text-align: center;
}

.code-status .success {
  color: #07c160;
}

.code-status .error {
  color: #ee0a24;
}

.promotion-notice {
  background: #fff7e6;
  border: 1rpx solid #ffd591;
  border-radius: 8rpx;
  padding: 20rpx;
}

.notice-text {
  font-size: 24rpx;
  color: #d48806;
  line-height: 1.5;
}

.modal-buttons {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 28rpx;
}

.modal-btn.cancel {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.modal-btn.confirm {
  color: #667eea;
  font-weight: bold;
}
