.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 标签样式 */
.tabs-container {
  margin-bottom: 20rpx;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 8rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: #007aff;
  color: #fff;
  font-weight: bold;
}

/* 服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.service-sn {
  font-size: 28rpx;
  color: #666;
}

.service-status {
  font-size: 26rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  background-color: #f0f0f0;
  border-radius: 20rpx;
}

/* 服务详情样式 */
.service-details {
  margin-bottom: 20rpx;
}

.service-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.service-detail-item:last-child {
  border-bottom: none;
}

.detail-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.service-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.service-spec {
  font-size: 24rpx;
  color: #666;
}

.detail-total {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 价格信息样式 */
.price-info {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
}

.price-label {
  font-size: 26rpx;
  color: #666;
}

.price-value {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

.price-row.discount .price-value {
  color: #ff6b35;
}

.price-row.total {
  padding-top: 15rpx;
  border-top: 1rpx solid #e5e5e5;
  margin-top: 10rpx;
}

.price-row.total .price-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.total-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 15rpx;
}

.time-label {
  font-size: 24rpx;
  color: #999;
}

/* 状态描述样式 */
.status-desc {
  margin-bottom: 20rpx;
}

.desc-text {
  font-size: 26rpx;
  color: #666;
  font-style: italic;
}

/* 操作按钮样式 */
.service-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  border: none;
  font-weight: bold;
}

.action-btn.primary {
  background-color: #007aff;
  color: #fff;
}

.action-btn.secondary {
  background-color: #f0f0f0;
  color: #666;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  padding: 16rpx 32rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 24rpx;
  font-size: 28rpx;
  border: none;
}

/* 底部操作区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  z-index: 100;
}

.apply-btn {
  width: 100%;
  height: 80rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
