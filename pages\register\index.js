import userApi from '../../api/modules/user.js';
import promotionRecordApi from '../../api/modules/promotionRecord.js';
import employeePromotionApi from '../../api/modules/employeePromotion.js';
import siteinfo from '../../siteinfo.js';

Page({
  data: {
    siteinfo,
    formData: {
      avatarUrl: '',
      nickName: '',
      phone: '',
      gender: '0',
      openid: '',
      promotionCode: '', // 推广码
    },
    globalData: {
      agree: '0',
    },
    showRegionPicker: false,
    isSubmitting: false,
    // 推广码相关
    promotionCodeInfo: null, // 推广码验证信息
    isCheckingCode: false, // 是否正在验证推广码
  },

  onLoad(options) {
    console.log(options);
    if (options.actionType === 'register') {
      this.navigateTo({
        type: 'tip',
        tip: '您还没有账号，请先注册',
      });
    }
    if (options.openid) {
      this.updateFormField('openid', options.openid);
    }
    this.initValidate();
  },

  // 获取微信用户信息
  async getUserProfile() {
    try {
      const { userInfo } = await wx.getUserProfile({
        desc: '用于完善用户资料',
      });

      this.setData({
        'formData.avatarUrl': userInfo.avatarUrl,
        'formData.nickName': userInfo.nickName,
        'formData.gender': `${userInfo.gender}`,
      });
    } catch (error) {
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none',
      });
    }
  },

  async getPhoneNumber(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      const { encryptedData, iv, code } = e.detail;
      console.log(encryptedData, iv, code);
      const data = await userApi.getPhoneNumber(code);
      console.log(data);
      if (!data || !data.phone_info || !data.phone_info.phoneNumber) {
        wx.showToast({
          title: '手机号获取失败',
          icon: 'none',
        });
        return;
      }
      this.setData({
        'formData.phone': data.phone_info.phoneNumber,
      });
    } else {
      wx.showToast({
        title: '获取手机号失败',
        icon: 'none',
      });
    }
  },

  // 统一的表单字段更新方法
  updateFormField(field, value) {
    this.setData({
      [`formData.${field}`]: value,
    });
  },

  // 使用统一的更新方法
  onChooseAvatar(e) {
    this.updateFormField('avatarUrl', e.detail.avatarUrl);
  },

  // 昵称输入
  onNickNameChange(e) {
    this.updateFormField('nickName', e.detail.value.trim());
  },

  // 性别选择
  onGenderChange(e) {
    this.updateFormField('gender', e.detail.value);
  },

  // 推广码输入
  onPromotionCodeChange(e) {
    const code = e.detail.value.trim().toUpperCase();
    this.updateFormField('promotionCode', code);

    // 清除之前的验证信息
    this.setData({
      promotionCodeInfo: null
    });

    // 如果推广码不为空，进行验证
    if (code) {
      this.checkPromotionCode(code);
    }
  },

  // 验证推广码
  async checkPromotionCode(code) {
    if (this.data.isCheckingCode) return;

    this.setData({ isCheckingCode: true });

    try {
      const result = await employeePromotionApi.checkCode(code);
      this.setData({
        promotionCodeInfo: result
      });
    } catch (error) {
      console.error('验证推广码失败:', error);
      this.setData({
        promotionCodeInfo: {
          available: false,
          message: '推广码验证失败，请稍后重试'
        }
      });
    } finally {
      this.setData({ isCheckingCode: false });
    }
  },

  async uploadAvatar(tempFilePath) {
    try {
      wx.showLoading({ title: '上传头像中...' });
      const result = await userApi.uploadFile(tempFilePath);
      return result.url;
    } catch (error) {
      wx.showToast({
        title: '头像上传失败，请重试',
        icon: 'none',
      });
      throw error;
    } finally {
      wx.hideLoading();
    }
  },

  // 初始化验证规则
  initValidate() {
    const rules = {
      openid: {
        required: {
          value: true,
          message: '缺少用户openid，请返回首页重新登录',
        },
      },
      nickName: {
        required: {
          value: true,
          message: '请输入昵称',
        },
        minlength: {
          value: 2,
          message: '昵称至少2个字符',
        },
        maxlength: {
          value: 12,
          message: '昵称最多12个字符',
        },
      },
      phone: {
        required: {
          value: true,
          message: '请输入手机号',
        },
        tel: {
          message: '请输入正确的手机号',
        },
      },
      avatarUrl: {
        required: {
          value: true,
          message: '请选择头像',
        },
      },
    };

    this.validate = this.Validate(rules);
  },

  // 表单提交
  async handleSubmit(e) {
    const _this = this;
    // 防止重复提交
    if (this.data.isSubmitting) {
      return;
    }

    this.setData({ isSubmitting: true });

    try {
      const formData = e.detail.value;

      // 数据trim处理
      formData.nickName = formData.nickName.trim();

      // 敏感字符过滤
      if (/[<>]/.test(formData.nickName)) {
        wx.showToast({
          title: '昵称包含非法字符',
          icon: 'none',
        });
        return;
      }

      // 表单验证
      if (!this.validate.checkForm(e)) {
        console.log(this.validate.errorList);
        const error = this.validate.errorList[0];
        wx.showToast({
          title: error.msg,
          icon: 'none',
        });
        return;
      }

      // 上传头像（如果是本地临时文件）
      let avatarUrl = formData.avatarUrl;
      if (avatarUrl.startsWith('wxfile://')) {
        avatarUrl = await this.uploadAvatar(avatarUrl);
      }

      wx.showLoading({ title: '注册中...' });
      const result = await userApi.register({
        ...formData,
        avatarUrl,
      });

      if (!result) {
        wx.showToast({
          title: '注册失败',
          icon: 'none',
        });
        return;
      }
      // 处理推广关系
      setTimeout(async () => {
        try {
          // 优先处理员工推广码
          if (formData.promotionCode && this.data.promotionCodeInfo?.available) {
            await employeePromotionApi.create({
              customerId: result.id,
              promotionCode: formData.promotionCode
            });
            console.log('员工推广关系建立成功');
          } else {
            // 处理用户推广
            const promoterInfo = wx.getStorageSync('promoterInfo');
            if (promoterInfo) {
              if (promoterInfo.shareCode) {
                promotionRecordApi.create({
                  /** 推广码 */
                  promotionCode: promoterInfo.shareCode,
                  /** 分享时间 */
                  shareTime: promoterInfo.shareTime,
                  /** 注册人系统用户ID */
                  registrantUserId: result.id,
                });
              } else if (promoterInfo.id) {
                promotionRecordApi.create({
                  /** 分享人系统用户ID */
                  sharerUserId: promoterInfo.id,
                  /** 分享时间 */
                  shareTime: promoterInfo.shareTime,
                  /** 注册人系统用户ID */
                  registrantUserId: result.id,
                });
              }
            }
          }
        } catch (error) {
          console.error('建立推广关系失败:', error);
        }
      }, 2000);

      this.dologin();
    } catch (error) {
      wx.showToast({
        title: error.message || '注册失败',
        icon: 'none',
      });
    } finally {
      this.setData({ isSubmitting: false });
    }
  },

  dologin() {
    const _this = this;
    this.navigateTo({
      type: 'login',
      // 表示用户端登录
      logintype: 'customer',
      callback: () => {
        _this.navigateTo({ type: 'page', url: '/pages/index/index' });
      },
    });
  },

  // agree 自定义方法
  async agreeFunction(param) {
    let that = this.data;
    //如果不同意，改为同意
    this.setData({
      globalData: {
        agree: that.globalData.agree == '1' ? '0' : '1',
      },
    });
  },

  // 未同意协议时点击按钮的处理方法
  handleAgreeFirst() {
    // 添加震动反馈
    wx.vibrateShort({
      type: 'medium',
    });
    wx.showToast({
      title: '请先阅读并同意用户协议和隐私协议',
      icon: 'none',
      duration: 2000,
    });
  },
});
