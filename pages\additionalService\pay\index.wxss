.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* 支付头部样式 */
.pay-header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  color: #fff;
}

.pay-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.pay-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.pay-desc {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
}

/* 通用区块样式 */
.service-section,
.price-section,
.order-section,
.customer-section,
.payment-method-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.service-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.service-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.service-spec {
  font-size: 24rpx;
  color: #666;
}

.service-total {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 价格明细样式 */
.price-details {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}

.price-row:not(:last-child) {
  border-bottom: 1rpx solid #f0f0f0;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.price-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.price-row.discount .price-value {
  color: #ff6b35;
}

/* 支付金额样式 */
.pay-amount-section {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
  color: #fff;
}

.amount-label {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  opacity: 0.9;
}

.amount-value {
  font-size: 48rpx;
  font-weight: bold;
}

/* 信息行样式 */
.order-info,
.customer-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
}

/* 支付方式样式 */
.payment-method {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.method-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.method-item.selected {
  border-color: #007aff;
  background-color: #f0f8ff;
}

.method-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.method-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.method-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.method-desc {
  font-size: 24rpx;
  color: #666;
}

.method-check {
  color: #007aff;
  font-size: 32rpx;
  font-weight: bold;
}

/* 支付按钮样式 */
.pay-button-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  z-index: 100;
}

.pay-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #007aff 0%, #0056d3 100%);
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.pay-btn:disabled {
  background: #ccc;
  box-shadow: none;
}

.detail-btn {
  width: 100%;
  height: 70rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 35rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 支付说明样式 */
.pay-notice {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.notice-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.notice-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
