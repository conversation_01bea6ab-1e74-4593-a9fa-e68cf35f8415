<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">申请追加服务</text>
    <text class="page-desc">
      <block wx:if="{{hasExistingApplication}}">查看您已申请的追加服务</block>
      <block wx:else>为您的宠物选择额外的服务项目</block>
    </text>
  </view>

  <!-- 已有申请提示 -->
  <view wx:if="{{hasExistingApplication}}" class="existing-notice">
    <view class="notice-icon">ℹ️</view>
    <view class="notice-content">
      <text class="notice-title">您已提交过追加服务申请</text>
      <text class="notice-desc">以下是您已申请的服务内容，如需修改请联系客服</text>
    </view>
  </view>

  <!-- 可用服务列表 -->
  <view class="section">
    <view class="section-title">选择服务</view>
    <view class="service-groups">
      <view wx:for="{{availableServices}}" wx:for-item="group" wx:key="id" class="service-group">
        <view class="group-title">{{group.name}}</view>
        <view class="service-list">
          <view wx:for="{{group.children}}" wx:key="id" class="service-item {{item.isSelected ? 'selected' : ''}} {{hasExistingApplication ? 'disabled' : ''}}" bindtap="{{hasExistingApplication ? '' : 'selectService'}}" data-service="{{item}}">
            <view class="service-info">
              <image wx:if="{{item.logo}}" src="{{item.logo}}" class="service-logo" mode="aspectFill"></image>
              <view class="service-details">
                <text class="service-name">{{item.name || '增项服务-' + item.id}}</text>
                <text class="service-price">¥{{item.price}}</text>
              </view>
            </view>
            <view class="service-action">
              <view wx:if="{{item.isSelected}}" class="selected-badge">
                <text class="selected-count">{{item.selectedQuantity}}</text>
              </view>
              <text class="add-btn {{item.isSelected ? 'selected' : ''}}">+</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 已选服务 -->
  <view wx:if="{{selectedServices.length > 0}}" class="section selected-section" id="selected-services-section">
    <view class="section-title selected-title">
      <text>已选服务</text>
      <text class="service-count">({{selectedServices.length}}项)</text>
    </view>
    <view class="selected-services">
      <view wx:for="{{selectedServices}}" wx:key="serviceId" class="selected-service-item">
        <view class="service-info">
          <text class="service-name">{{item.service.name || '增项服务-' + item.service.id}}</text>
          <text class="service-price">¥{{item.service.price}} × {{item.quantity}}</text>
        </view>
        <view class="quantity-controls">
          <view class="quantity-btn {{hasExistingApplication ? 'disabled' : ''}}" bindtap="{{hasExistingApplication ? '' : 'adjustServiceQuantity'}}" data-service-id="{{item.serviceId}}" data-action="decrease">-</view>
          <text class="quantity">{{item.quantity}}</text>
          <view class="quantity-btn {{hasExistingApplication ? 'disabled' : ''}}" bindtap="{{hasExistingApplication ? '' : 'adjustServiceQuantity'}}" data-service-id="{{item.serviceId}}" data-action="increase">+</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 优惠选择 -->
  <view wx:if="{{selectedServices.length > 0}}" class="section">
    <view class="section-title">优惠选择</view>
    <view class="discount-section" bindtap="showDiscountSelector">
      <view class="discount-info {{hasExistingApplication ? 'disabled' : ''}}" bindtap="{{hasExistingApplication ? '' : 'showDiscountSelector'}}">
        <text class="discount-label">优惠券</text>
        <text class="discount-desc">
          <block wx:if="{{selectedCouponId}}">
            已选择优惠券
          </block>
          <block wx:elif="{{hasExistingApplication}}">
            已申请时的优惠券
          </block>
          <block wx:else>
            点击选择优惠券
          </block>
        </text>
      </view>
      <text class="arrow">></text>
    </view>
  </view>

  <!-- 备注 -->
  <view wx:if="{{selectedServices.length > 0}}" class="section">
    <view class="section-title">备注说明</view>
    <textarea
      class="remark-input {{hasExistingApplication ? 'disabled' : ''}}"
      placeholder="{{hasExistingApplication ? '已申请的备注信息' : '请输入特殊需求或备注信息（选填）'}}"
      value="{{remark}}"
      bindinput="{{hasExistingApplication ? '' : 'onRemarkInput'}}"
      disabled="{{hasExistingApplication}}"
      maxlength="200"
    ></textarea>
  </view>

  <!-- 价格汇总 -->
  <view wx:if="{{selectedServices.length > 0}}" class="price-summary">
    <view class="price-row">
      <text class="price-label">服务原价</text>
      <text class="price-value">¥{{originalPrice}}</text>
    </view>
    <view wx:if="{{cardDeduction > 0}}" class="price-row discount">
      <text class="price-label">权益卡优惠</text>
      <text class="price-value">-¥{{cardDeduction}}</text>
    </view>
    <view wx:if="{{couponDeduction > 0}}" class="price-row discount">
      <text class="price-label">优惠券优惠</text>
      <text class="price-value">-¥{{couponDeduction}}</text>
    </view>
    <view class="price-row total">
      <text class="price-label">实付金额</text>
      <text class="price-value total-price">¥{{totalFee}}</text>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view wx:if="{{selectedServices.length > 0}}" class="submit-section">
    <button wx:if="{{hasExistingApplication}}" class="submit-btn disabled" disabled="true">
      已提交申请
    </button>
    <button wx:else class="submit-btn" bindtap="submitApplication" disabled="{{loading}}">
      {{loading ? '提交中...' : '提交申请'}}
    </button>

    <!-- 查看申请记录按钮 -->
    <button wx:if="{{hasExistingApplication}}" class="view-list-btn" bindtap="viewApplicationList">
      查看申请记录
    </button>
  </view>

  <!-- 优惠选择弹窗 -->
  <view wx:if="{{showDiscountSelector}}" class="discount-modal">
    <view class="discount-modal-content">
      <view class="modal-header">
        <text class="modal-title">选择优惠</text>
        <view class="close-btn" bindtap="hideDiscountSelector">×</view>
      </view>
      


      <!-- 优惠券 -->
      <view wx:if="{{availableCoupons.length > 0}}" class="discount-category">
        <view class="category-title">优惠券</view>
        <view class="discount-list">
          <view wx:for="{{availableCoupons}}" wx:key="id"
                class="discount-item {{selectedCouponId === item.id ? 'selected' : ''}}"
                bindtap="selectCoupon" data-coupon-id="{{item.id}}">
            <view class="discount-item-left">
              <view class="discount-item-name">{{item.coupon.description || '优惠券'}}</view>
              <view class="discount-item-desc">
                <text wx:if="{{item.threshold}}">满{{item.threshold}}元减{{item.amount}}元</text>
                <text wx:else>直减{{item.amount}}元</text>
              </view>
              <view wx:if="{{item.expiryTime}}" class="discount-item-date">有效期至: {{item.expiryTime}}</view>
              <view class="discount-item-desc">剩余次数: {{item.remainTimes === -1 ? '不限' : item.remainTimes}}次</view>
              <view class="discount-item-tip">
                <text wx:if="{{originalPrice < item.amount}}">最高可抵扣{{originalPrice}}元</text>
                <text wx:else>最高可抵扣{{item.amount}}元</text>
              </view>
            </view>
            <view class="discount-item-right">
              <view class="discount-item-value">￥{{item.amount}}</view>
              <view class="discount-item-select">
                <text wx:if="{{selectedCouponId === item.id}}">✓</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="modal-actions">
        <button class="modal-btn confirm-btn" bindtap="hideDiscountSelector">确定</button>
      </view>
    </view>
  </view>

  <!-- 浮动的已选服务提示 -->
  <view wx:if="{{selectedServices.length > 0}}" class="floating-summary">
    <view class="summary-content" bindtap="scrollToSelectedServices">
      <text class="summary-text">已选{{selectedServices.length}}项服务</text>
      <!-- <text class="summary-price">¥{{totalFee}}</text> -->
    </view>
  </view>

  <!-- 自定义模态框 -->
  <custom-modal
    show="{{showModal}}"
    title="{{modalTitle}}"
    content="{{modalContent}}"
    buttons="{{modalButtons}}"
    bind:confirm="handleModalConfirm"
    bind:cancel="handleModalCancel"
    bind:modalConfirm="handleModalConfirm"
    bind:modalCancel="handleModalCancel"
  ></custom-modal>
</view>
