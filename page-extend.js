/**
 * Page扩展函数
 * @param {*} Page 原生Page
 */
import Tools from './common/Tools.js';
import $request from './api/request.js';
import userApi from './api/modules/user.js';
import zosApi from './api/modules/zos.js';
import Session from './common/Session.js';
import ValidateClazz from './common/Validate';

const pageExtend = Page => {
  return object => {
    // 导出原生Page传入的object参数中的生命周期函数
    // 由于命名冲突，所以将onLoad生命周期函数命名成了onLoaded
    const { onLoad } = object;

    object.getOption = function (option) {
      if (option !== null && typeof option === 'object') {
        for (let key in option) {
          option[key] = decodeURIComponent(option[key]);
        }
      }
      return option;
    };
    // // 公共的onLoad生命周期函数
    object.onLoad = function (options) {
      var thiz = this;
      if (options) {
        this.setData({
          globalOption: this.getOption(options),
        });
      }
      // 判断用户是否已登录，如果已登录设置用户信息
      if (this.$session?.getUser()) {
        this.setData({
          userInfo: this.$session.getUser(),
        });
      }

      // 执行onLoaded生命周期函数
      if (typeof onLoad === 'function') {
        onLoad.call(thiz, options);
      }
    };

    object.$tools = new Tools();
    object.$session = Session;

    object.navigateTo = function (e) {
      let thiz = this;
      let dataset = e.currentTarget ? e.currentTarget.dataset : e;
      let { id, type } = dataset;
      if (type == 'openmodal') {
        this.setData({
          [id]: 'open',
        });
        setTimeout(() => {
          this.setData({
            [id]: 'show',
          });
        }, 1);
      } else if (type == 'closemodal') {
        this.setData({
          [id]: '',
        });
      } else if (type == 'page' || type == 'inner' || type == 'href') {
        this.$tools.navigateTo(dataset.url, dataset);
      } else if (type == 'submit') {
        this.showToast('将执行表单提交动作');
      } else if (type == 'reset') {
        this.showToast('将执行表单重置动作');
      } else if (type == 'tip') {
        this.showToast(dataset.tip);
      } else if (type == 'confirm') {
        wx.showModal({
          title: '提示',
          content: dataset.tip,
          showCancel: !1,
        });
      } else if (type == 'daohang') {
        wx.openLocation({
          latitude: Number(dataset.lat),
          longitude: Number(dataset.lng),
          address: dataset.address,
          success: function () {
            console.log('success');
          },
        });
      } else if (type == 'phone') {
        this.$tools.makePhoneCall(e);
      } else if (type == 'previewImage' || type == 'preview') {
        wx.previewImage({
          current: this.$tools.renderImage(dataset.img), // 当前显示图片的http链接
          urls: [this.$tools.renderImage(dataset.img)], // 需要预览的图片http链接列表
        });
      } else if (type == 'copy') {
        wx.setClipboardData({
          data: dataset.copy,
          showToast: false,
          success: function () {
            thiz.showToast(dataset.tip || '复制成功', 'none');
          },
        });
      } else if (type == 'xcx') {
        wx.navigateToMiniProgram({
          appId: dataset.appid,
          path: dataset.path,
          success(res) {
            // 打开成功
          },
        });
      } else if (typeof thiz[type] == 'function') {
        thiz[type](dataset);
      } else if (type == 'login') {
        let thiz = this;
        wx.login({
          success: function (res) {
            let data = {
              code: res.code,
              logintype: dataset.logintype,
            };
            userApi.login(data).then(data => {
              console.log(data);
              const { openid, user, token } = data || {};
              if (!openid) {
                thiz.navigateTo({
                  type: 'tip',
                  tip: '登录失败，请稍后重试',
                });
                return;
              }
              if (!user) {
                // 用户不存在，需要注册
                thiz.navigateTo({
                  type: 'page',
                  url: '/pages/register/index',
                  actionType: 'register',
                  openid,
                });
                return;
              }
              const userInfo = {
                ...user,
                token,
              };
              thiz.setData({ userInfo });
              thiz.$session.setUser(userInfo);
              if (dataset.callback) {
                dataset.callback(user);
              }
            });
          },
          fail: function () {
            wx.showModal({
              title: '获取用户信息',
              content: '请允许授权以便为您提供给服务',
              success: function (res) {
                if (res.confirm) {
                  thiz.navigateTo(dataset);
                }
              },
            });
          },
        });
      } else {
        thiz.showToast(type + '类型有待实现');
      }
    };

    object.showModal = function (message, title = '提示', iscancel = true) {
      return new Promise(resolve => {
        wx.showModal({
          title: title,
          content: message,
          showCancel: iscancel,
          success: function (res) {
            if (res.confirm) {
              resolve(true);
            } else if (res.cancel) {
              resolve(false);
            }
          },
        });
      });
    };

    object.showToast = function (title, icon) {
      wx.showToast({
        title: title,
        icon: icon ? icon : 'none',
      });
    };
    object.Validate = (rules, messages) => new ValidateClazz(rules, messages);
    object.getPickerChildren = function (data, chindInex1, childIndex2) {
      if (chindInex1 != null && data[chindInex1] && data[chindInex1].children && data[chindInex1].children) {
        let children = data[chindInex1].children;
        //只判断一级
        if (childIndex2 == null) {
          if (children != null && children.length > 0) {
            return children.map(item => item.label);
          } else {
            return [];
          }
        } else {
          //判断二级
          //有可能并设置下级结点
          if (children[childIndex2] == null) {
            return [];
          }
          let children2 = children[childIndex2].children;
          if (children2 != null && children2.length > 0) {
            return children2.map(item => item.text);
          } else {
            return [];
          }
        }
      } else {
        return [];
      }
    };

    object.changeValue = function (e) {
      const { key } = e.currentTarget.dataset;
      if (typeof e.detail == 'object' && this.$tools.isArray(e.detail)) {
        this.setData({
          [key]: e.detail,
        });
      } else if (typeof e.detail == 'object' && e.detail.hasOwnProperty('value')) {
        this.setData({
          [key]: e.detail.value,
        });
      } else {
        this.setData({
          [key]: e.detail,
        });
      }
    };

    object.uploadImage = function (
      thiz,
      field, // 想把url存入哪个字段，仅在type为avatar时有效
      keyPrefix = '',
      count = 9
    ) {
      return new Promise((resolve, reject) => {
        wx.chooseMedia({
          count: count, // 默认9
          mediaType: ['image'],
          sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
          sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
          success: async function (res) {
            try {
              // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
              let tempFilePaths = res.tempFiles;

              // 使用 Promise.all 等待所有图片上传完成
              const uploadPromises = tempFilePaths.map(async (tempFile) => {
                const currentFilePath = tempFile.tempFilePath;
                console.log('currentFilePath', currentFilePath);
                // 若是多个图片  则需要循环上传
                const currentKey = keyPrefix + currentFilePath.substring(currentFilePath.lastIndexOf('/') + 1);

                try {
                  // 获取上传链接
                  const uploadLinkRes = await zosApi.getUploadLink(currentKey);
                  const { url, fields } = uploadLinkRes?.returnObj || {};
                  if (!url || !fields) {
                    thiz.showToast('上传失败，请稍后重试');
                    throw new Error('获取上传链接失败');
                  }

                  // 上传文件
                  await new Promise((uploadResolve, uploadReject) => {
                    wx.uploadFile({
                      url: $request.setUrl(url),
                      filePath: currentFilePath,
                      name: 'file',
                      header: {
                        'content-type': 'multipart/form-data',
                      },
                      formData: fields,
                      success(uploadRes) {
                        if (uploadRes.statusCode != 204) {
                          thiz.showToast('上传失败，请稍后重试');
                          console.log(uploadRes);
                          uploadReject(new Error('上传失败'));
                          return;
                        }
                        uploadResolve(uploadRes);
                      },
                      fail(err) {
                        console.log('上传失败: ', err);
                        thiz.showToast('上传失败，请稍后重试');
                        uploadReject(err);
                      }
                    });
                  });

                  console.log('currentKey', currentKey);
                  // 上传成功后设置图片访问权限
                  await Promise.all([zosApi.setObjectHeaders(currentKey), zosApi.setObjectACL(currentKey)]);
                  console.log('图片访问权限设置成功');
                  const finalUrl = zosApi.getLink(currentKey);
                  return finalUrl;

                } catch (err) {
                  console.log('处理图片失败: ', err);
                  thiz.showToast('上传失败，请稍后重试');
                  throw err;
                }
              });

              // 等待所有上传完成
              const uploadedUrls = await Promise.all(uploadPromises);
              resolve(uploadedUrls);

            } catch (error) {
              console.error('上传过程中发生错误:', error);
              reject(error);
            }
          },
          fail(err) {
            console.log('选择图片失败:', err);
            reject(err);
          }
        });
      });
    };

    return Page(object);
  };
};

// 获取原生Page
const originalPage = Page;
// 定义一个新的Page，将原生Page传入Page扩展函数
Page = pageExtend(originalPage);
