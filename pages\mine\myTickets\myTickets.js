// pages/mine/myTickets/myTickets.js
import rightsCardApi from '../../../api/modules/rightsCard';
import couponApi from '../../../api/modules/coupon';
import util from '../../utils/util';
import { UserCardStatus } from '../../../common/constant';

Page({
  data: {
    userInfo: null, // 用户信息
    hasRights_nk: false, // 是否有权益卡年卡
    rightsList_nk: [],
    hasRights_ck: false, // 是否有权益卡次卡
    rightsList_ck: [],
    hasCoupons: false, // 是否有优惠券
    couponsList: [],
    hasExpired: false, // 是否有已失效
    expiredList: [],
    userInfo: null,
    input: '',
    searchAble: false,
  },

  onLoad() {
    this.fetchRightsList();
    this.fetchCoupons();
  },

  // 查询我的权益卡
  async fetchRightsList() {
    const userInfo = this.data.userInfo;

    // 检查用户是否登录
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index',
      });
      return;
    }
    const res = await rightsCardApi.myCards(userInfo.id);
    const list = (res?.list || []).map(item => ({
      ...item,
      expiryTime: util.formatNormalDate(item.expiryTime),
      remainTimes:
        item.remainTimes === null || item.remainTimes === undefined || item.remainTimes === -1
          ? '不限'
          : item.remainTimes,
      status: UserCardStatus[item.status],
    }));
    const nkList = [];
    const ckList = [];
    const expiredList = [];
    for (const item of list) {
      console.log(item);
      if (item.status === UserCardStatus.active) {
        if (item.cardType.type === 'discount') {
          nkList.push(item);
        }
        if (item.cardType.type === 'times') {
          ckList.push(item);
        }
      } else {
        expiredList.push(item);
      }
    }
    const currentExpiredList = this.data.expiredList.concat(expiredList);
    this.setData({
      rightsList_nk: nkList,
      hasRights_nk: !!nkList.length,
      rightsList_ck: ckList || [],
      hasRights_ck: !!ckList.length,
      expiredList: currentExpiredList,
      hasExpired: !!currentExpiredList.length,
    });
  },

   // 查询我的优惠券
  async fetchCoupons() {
    const userInfo = this.data.userInfo;

    // 检查用户是否登录
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index',
      });
      return;
    }
    const res = await couponApi.myCoupons(userInfo.id);
    const list = (res?.list || []).map(item => ({
      ...item,
      amount: Number(item.coupon.amount).toFixed(0),
      threshold: item.coupon.threshold != 0 ? `满${Number(item.coupon.threshold).toFixed(0)}可用` : '不限',
      expiryTime: util.formatNormalDate(item.expiryTime),
      remainTimes:
        item.remainTimes === null || item.remainTimes === undefined || item.remainTimes === -1
          ? '不限'
          : item.remainTimes,
    }));
    const couponsList = [];
    const expiredList = [];
    for (const item of list) {
      if (item.status === 'active') {
        couponsList.push(item);
      } else {
        expiredList.push(item);
      }
    }
    const currentExpiredList = this.data.expiredList.concat(expiredList);
    this.setData({
      couponsList,
      hasCoupons: !!couponsList.length,
      expiredList: currentExpiredList,
      hasExpired: !!currentExpiredList.length,
    });
  },

  toggleCouponRules(e) {
    const couponId = e.currentTarget.dataset.id;
    const { coupons } = this.data;

    const updatedCoupons = coupons.map(coupon => {
      if (coupon.id === couponId) {
        coupon.showRules = !coupon.showRules;
      }
      return coupon;
    });

    this.setData({
      coupons: updatedCoupons,
    });
  },

  changeInputValue(e) {
    const val = e.detail.value;
    if (val && val.trim() !== '') {
      this.setData({
        input: val,
        searchAble: true,
      });
    } else {
      this.setData({
        searchAble: false,
        input: '',
      });
    }
  },
  redirect(evt) {
    let { type } = evt.currentTarget.dataset;
    wx.navigateTo({
      url: '/pages/mine/' + type + '/' + type,
    });
  },

  goToUse() {
    wx.navigateTo({
      url: '/pages/service/index',
    });
  },
});
