// pages/mine/privacy/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    protocol: {
      title: '贝宠约洗隐私协议',
      sections: [
        {
          title: '一、信息收集范围',
          subsections: [
            {
              title: '用户个人信息',
              content: [
                '基础信息: 姓名、联系方式 (手机号/微信号)、服务地址、支付账户信息 (如微信支付授权)。',
                '身份验证信息: 身份证号（仅限需开具发票或涉及保险理赔时收集）。',
              ],
            },
            {
              title: '宠物信息',
              content: [
                '基础档案: 宠物品种、年龄、体重、毛色、照片；',
                '健康数据: 疫苗接种记录、过敏史、病史、服务偏好 (如洗护习惯、禁用产品)；',
                '行为特征: 攻击性、应激反应记录 (需用户主动填报)。',
              ],
            },
            {
              title: '服务与设备数据',
              content: [
                '服务记录: 预约时间、服务项目、消费金额、评价反馈；',
                '地理位置: 基于 GPS 或 IP 地址获取，用于洗护车调度及服务范围验证；',
                '设备信息: 手机型号、操作系统、小程序使用日志 (用于故障排查)。',
              ],
            },
          ],
        },
        {
          title: '二、信息使用目的',
          subsections: [
            {
              title: '核心服务功能',
              content: ['完成预约、支付、订单管理及售后服务；', '向服务人员提供宠物健康数据，确保洗护安全。'],
            },
            {
              title: '服务优化与个性化',
              content: [
                '分析用户偏好，推送定制化服务 (如节日促销、毛发护理建议)；',
                '通过匿名化数据改进洗护流程、优化车辆调度效率。',
              ],
            },
            {
              title: '法律与安全义务',
              content: ['配合公安、市场监管等部门依法调取数据；', '防范欺诈、刷单等违法违规行为，保障平台安全。'],
            },
          ],
        },
        {
          title: '三、数据共享与保护',
          subsections: [
            {
              title: '数据共享场景',
              content: [
                '服务人员: 向洗护师提供宠物健康记录、服务地址及联系方式；',
                '第三方合作方: 支付机构 (微信支付) 处理交易；',
                '云服务商存储数据 (需签署保密协议)；',
                '宠物医院 (仅在紧急医疗情况下共享健康数据)；',
                '法律强制披露: 应法院、政府部门要求提供必要信息。',
              ],
            },
            {
              title: '数据保护措施',
              content: [
                '技术防护: SSL 加密传输、数据库脱敏存储、定期漏洞扫描；',
                '权限管控：严格限制内部人员访问权限，操作日志留痕追溯；',
                '数据留存：用户信息保存至账户注销后6个月，宠物健康数据留存3年（用于纠纷处理）。',
              ],
            },
          ],
        },
        {
          title: '四、用户权利',
          subsections: [
            {
              title: '知情与控制权',
              content: ['关闭非必要权限（如地理位置），但可能影响服务可用性。'],
            },
            {
              title: '更正与删除权',
              content: [
                '修改宠物档案或删除错误健康记录；',
                '注销账户后，除法律要求留存的数据外，其余信息将被彻底删除。',
              ],
            },
            {
              title: '拒绝与投诉权',
              content: [
                '拒绝接收营销推送（通过短信退订或小程序设置）；',
                '对数据滥用行为，可通过客服热线（18591969898）或邮箱（<EMAIL>）投诉，平台将在15个工作日内反馈处理结果。',
              ],
            },
          ],
        },
        {
          title: '五、特别条款',
          subsections: [
            {
              title: '敏感信息保护',
              content: ['宠物健康数据、用户身份证号属于敏感信息，仅限必要场景收集，且需用户单独授权。'],
            },
            {
              title: '未成年人保护',
              content: ['未成年人使用服务需监护人同意，监护人可申请删除未成年人相关数据。'],
            },
            {
              title: '协议更新',
              content: ['本协议修订将通过小程序弹窗或站内通知公示，重大变更将提前7日提醒。'],
            },
          ],
        },
      ],
      description: '本协议自用户勾选同意后生效，与《用户协议》具有同等法律效力。',
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  onShareAppMessage: function () {
    return {
      title: '贝宠约洗，足不出户享精致洗护～',
      path: `/pages/index/index?aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share.png',
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },
  onShareTimeline: function () {
    return {
      title: '「忙到没空送洗？上门服务解忧！」贝宠约洗，专业到家超省心～',
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share1.png',
      query: `aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },
});
