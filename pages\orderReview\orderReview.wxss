/* pages/orderReview/orderReview.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 页面标题 */
.header {
  background-color: #fff;
  padding: 40rpx 0;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 订单信息 */
.order-info {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.order-content {
  display: flex;
  align-items: center;
}

.service-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}

.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.service-price {
  font-size: 28rpx;
  color: #ff4f8f;
  font-weight: 600;
}

/* 评分区域 */
.rating-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.rating-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stars {
  display: flex;
  margin-bottom: 20rpx;
}

.star {
  margin: 0 8rpx;
  transition: all 0.3s ease;
}

.star-icon {
  font-size: 60rpx;
  color: #ddd;
  transition: all 0.3s ease;
}

.star.active .star-icon {
  color: #ffd700;
  text-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);
}

.rating-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.rating-score {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff4f8f;
}

/* 评价内容 */
.comment-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

.comment-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.6;
  background-color: #fafafa;
}

.comment-input:focus {
  border-color: #ff4f8f;
  background-color: #fff;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

/* 图片上传 */
.photo-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.optional-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

.photo-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
  gap: 16rpx;
}

.photo-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.photo-preview {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  border: 2rpx solid #f0f0f0;
}

.photo-delete {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}

.delete-icon {
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
}

.photo-upload {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.photo-upload:active {
  background-color: #f0f0f0;
  border-color: #ff4f8f;
}

.upload-icon {
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 40rpx;
  color: #999;
}

.upload-label {
  font-size: 22rpx;
  color: #999;
}

.photo-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
  text-align: center;
}

/* 提交按钮 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  background-color: #ddd;
  color: #999;
  border: none;
  transition: all 0.3s ease;
}

.submit-btn.active {
  background: linear-gradient(135deg, #ff4f8f 0%, #ff7ba7 100%);
  color: #fff;
  box-shadow: 0 6rpx 20rpx rgba(255, 79, 143, 0.3);
}

.submit-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 79, 143, 0.3);
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: #fff;
  padding: 60rpx 80rpx;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #ff4f8f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
