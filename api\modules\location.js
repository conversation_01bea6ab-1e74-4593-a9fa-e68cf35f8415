import request, { analysisRes } from '../request';
import config from '../config';

const { apiUrls } = config;
export default {
  /**
   * 查找附近车辆
   * @param {object} data
   * @param {number} data.lat 纬度
   * @param {number} data.lng 经度
   * @param {number} [data.radius] 半径，单位：米， 默认：10000
   * @returns {any[]} 车辆列表
   */
  async findNearbyVehicles(data) {
    const res = await request.get(apiUrls.location.findNearbyVehicles, data);
    return analysisRes(res);
  },
  /**
   * 计算两个经纬度之间的距离
   * @param {object} data
   * @param {number} data.lat1 起点纬度
   * @param {number} data.lng1 起点经度
   * @param {number} data.lat2 终点纬度
   * @param {number} data.lng2 终点经度
   * @returns {number} 距离，单位：米
   */
  async calculateDistance(data) {
    const res = await request.post(apiUrls.location.calculateDistance, data);
    return analysisRes(res);
  },
};
