/* components/discount-selector/discount-selector.wxss */
/* 修改为居中弹出的模态框样式 */
.discount-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.discount-selector-modal.show {
  visibility: visible;
  opacity: 1;
}

.discount-selector-container {
  position: relative;
  width: 80%;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.discount-selector-modal.show .discount-selector-container {
  transform: scale(1);
}

.discount-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.discount-selector-title {
  font-size: 32rpx;
  font-weight: bold;
}

.discount-selector-close {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.discount-selector-tabs {
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.discount-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.discount-tab.active {
  color: #ff4d94;
}

.discount-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff4d94;
}

.discount-selector-content {
  max-height: 60vh;
  overflow-y: auto;
  padding-bottom: 20rpx;
}

.discount-none {
  padding: 40rpx;
  text-align: center;
  color: #999;
}

.discount-list {
  padding: 20rpx;
}

.discount-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 10rpx;
  background-color: #f9f9f9;
  border: 1rpx solid #eee;
}

.discount-item.selected {
  border-color: #ff4d94;
  background-color: #fff5f8;
}

.discount-item-left {
  flex: 1;
}

.discount-item-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.discount-item-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.discount-item-date {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.discount-item-tip {
  font-size: 24rpx;
  color: #ff4d94;
}

.discount-item-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 20rpx;
}

.discount-item-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4d94;
  margin-bottom: 10rpx;
}

.discount-item-select {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 1rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
}

.discount-item.selected .discount-item-select {
  background-color: #ff4d94;
  border-color: #ff4d94;
}

.discount-empty {
  padding: 40rpx;
  text-align: center;
  color: #999;
}

.discount-selector-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  background-color: #fff;
}

.discount-selector-total {
  display: flex;
  align-items: center;
}

.discount-selector-total-label {
  font-size: 28rpx;
  color: #333;
}

.discount-selector-total-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4d94;
}

.discount-selector-btn {
  width: 200rpx;
  height: 70rpx;
  background-color: #ff4d94;
  color: #fff;
  font-size: 28rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
