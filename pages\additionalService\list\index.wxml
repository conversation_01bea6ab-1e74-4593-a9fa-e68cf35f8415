<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">追加服务</text>
    <text class="page-desc">查看您的追加服务申请记录</text>
  </view>

  <!-- 状态筛选标签 -->
  <view class="tabs-container">
    <scroll-view scroll-x class="tabs-scroll">
      <view class="tabs">
        <view wx:for="{{tabs}}" wx:key="key" 
              class="tab-item {{currentTab === item.key ? 'active' : ''}}"
              bindtap="switchTab" data-key="{{item.key}}">
          {{item.label}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 追加服务列表 -->
  <view class="service-list">
    <block wx:if="{{!loading && additionalServices.length > 0}}">
      <view wx:for="{{additionalServices}}" wx:key="id" class="service-item" bindtap="viewDetail" data-id="{{item.id}}">
        <!-- 服务头部信息 -->
        <view class="service-header">
          <view class="service-sn">订单号：{{item.sn}}</view>
          <view class="service-status" style="color: {{item.statusInfo.color}}">
            {{item.statusInfo.text}}
          </view>
        </view>

        <!-- 服务详情 -->
        <view class="service-details">
          <view wx:for="{{item.details}}" wx:key="id" wx:for-item="detail" class="service-detail-item">
            <view class="detail-info">
              <text class="service-name">{{detail.serviceName}}</text>
              <text class="service-spec">数量：{{detail.quantity}} | 单价：¥{{detail.servicePrice}}</text>
            </view>
            <text class="detail-total">¥{{detail.servicePrice * detail.quantity}}</text>
          </view>
        </view>

        <!-- 价格信息 -->
        <view class="price-info">
          <view class="price-row">
            <text class="price-label">原价</text>
            <text class="price-value">¥{{item.originalPrice}}</text>
          </view>
          <view wx:if="{{item.cardDeduction > 0}}" class="price-row discount">
            <text class="price-label">权益卡优惠</text>
            <text class="price-value">-¥{{item.cardDeduction}}</text>
          </view>
          <view wx:if="{{item.couponDeduction > 0}}" class="price-row discount">
            <text class="price-label">优惠券优惠</text>
            <text class="price-value">-¥{{item.couponDeduction}}</text>
          </view>
          <view class="price-row total">
            <text class="price-label">实付金额</text>
            <text class="price-value total-price">¥{{item.totalFee}}</text>
          </view>
        </view>

        <!-- 时间信息 -->
        <view class="time-info">
          <text class="time-label">申请时间：{{item.createdAt}}</text>
          <text wx:if="{{item.confirmTime}}" class="time-label">确认时间：{{item.confirmTime}}</text>
        </view>

        <!-- 状态描述 -->
        <view class="status-desc">
          <text class="desc-text">{{item.statusInfo.desc}}</text>
        </view>

        <!-- 操作按钮 -->
        <view class="service-actions" catchtap="stopPropagation">
          <block wx:if="{{item.status === 'confirmed'}}">
            <button class="action-btn primary" bindtap="goPay" data-id="{{item.id}}">
              去支付
            </button>
          </block>
          <button class="action-btn secondary" bindtap="viewDetail" data-id="{{item.id}}">
            查看详情
          </button>
        </view>
      </view>
    </block>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{!loading && additionalServices.length === 0}}" class="empty-container">
      <image src="https://xian7.zos.ctyun.cn/pet/static/empty.png" class="empty-image" mode="aspectFit"></image>
      <text class="empty-text">暂无追加服务记录</text>
      <button class="empty-btn" bindtap="applyNewService">申请追加服务</button>
    </view>
  </view>

  <!-- 底部申请按钮 -->
  <view wx:if="{{additionalServices.length > 0}}" class="bottom-actions">
    <button class="apply-btn" bindtap="applyNewService">申请新的追加服务</button>
  </view>

  <!-- 阻止事件冒泡的辅助方法 -->
  <wxs module="utils">
    var stopPropagation = function(event) {
      event.stopPropagation();
    };
    module.exports = {
      stopPropagation: stopPropagation
    };
  </wxs>

  <!-- 自定义模态框 -->
  <custom-modal
    show="{{showModal}}"
    title="{{modalTitle}}"
    content="{{modalContent}}"
    buttons="{{modalButtons}}"
    bind:confirm="handleModalConfirm"
    bind:cancel="handleModalCancel"
    bind:modalConfirm="handleModalConfirm"
    bind:modalCancel="handleModalCancel"
  ></custom-modal>
</view>
