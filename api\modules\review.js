import request, { analysisRes } from '../request';
import config from '../config';

const { apiUrls } = config;

export default {
  /**
   * 创建评价
   * @param {string} customerId 客户ID
   * @param {object} reviewData 评价数据
   * @param {number} reviewData.orderId 订单ID
   * @param {number} reviewData.rating 评分 (1-5)
   * @param {string} reviewData.comment 评价内容
   * @param {array} reviewData.photoURLs 图片URL数组
   * @returns {Promise<any>} 返回评价结果
   */
  async create(customerId, reviewData) {
    const res = await request.post(
      apiUrls.review.create.replace('{customerId}', customerId), 
      reviewData
    );
    return analysisRes(res);
  },

  /**
   * 获取订单评价
   * @param {string} orderId 订单ID
   * @returns {Promise<any>} 返回评价信息
   */
  async getByOrderId(orderId) {
    const res = await request.get(
      apiUrls.review.getByOrderId.replace('{orderId}', orderId)
    );
    return analysisRes(res);
  },

  /**
   * 获取客户的评价列表
   * @param {string} customerId 客户ID
   * @param {object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @returns {Promise<any>} 返回评价列表
   */
  async getCustomerReviews(customerId, params = {}) {
    const res = await request.get(
      apiUrls.review.customerList.replace('{customerId}', customerId),
      params
    );
    return analysisRes(res);
  },

  /**
   * 获取服务的评价列表
   * @param {string} serviceId 服务ID
   * @param {object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @returns {Promise<any>} 返回评价列表
   */
  async getServiceReviews(serviceId, params = {}) {
    const res = await request.get(
      apiUrls.review.serviceList.replace('{serviceId}', serviceId),
      params
    );
    return analysisRes(res);
  },

  /**
   * 更新评价
   * @param {string} customerId 客户ID
   * @param {string} reviewId 评价ID
   * @param {object} reviewData 更新的评价数据
   * @returns {Promise<any>} 返回更新结果
   */
  async update(customerId, reviewId, reviewData) {
    const res = await request.put(
      apiUrls.review.update.replace('{customerId}', customerId).replace('{reviewId}', reviewId),
      reviewData
    );
    return analysisRes(res);
  },

  /**
   * 删除评价
   * @param {string} customerId 客户ID
   * @param {string} reviewId 评价ID
   * @returns {Promise<any>} 返回删除结果
   */
  async delete(customerId, reviewId) {
    const res = await request.delete(
      apiUrls.review.delete.replace('{customerId}', customerId).replace('{reviewId}', reviewId)
    );
    return analysisRes(res);
  }
};
