import request, { analysisRes } from '../request';
import config from '../config';

const { additionalService } = config.apiUrls;

export default {
  /**
   * 创建追加服务申请
   * @param {number} orderDetailId 订单详情ID
   * @param {object} data 申请数据
   * @param {number} data.customerId 客户ID
   * @param {array} data.services 追加服务列表
   * @param {array} data.discountInfos 优惠信息列表
   * @param {string} data.remark 备注
   * @returns {Promise<any>} 返回创建结果
   */
  async create(orderDetailId, data) {
    const res = await request.post(
      additionalService.create.replace('{orderDetailId}', orderDetailId),
      data
    );
    return analysisRes(res);
  },

  /**
   * 查询追加服务列表
   * @param {number} orderDetailId 订单详情ID
   * @param {object} params 查询参数
   * @param {string} params.status 状态筛选
   * @returns {Promise<any>} 返回追加服务列表
   */
  async list(orderDetailId, params = {}) {
    const res = await request.get(
      additionalService.list.replace('{orderDetailId}', orderDetailId),
      params
    );
    return analysisRes(res);
  },

  /**
   * 查询追加服务详情
   * @param {number} orderDetailId 订单详情ID
   * @param {number} id 追加服务订单ID
   * @returns {Promise<any>} 返回追加服务详情
   */
  async detail(orderDetailId, id) {
    const res = await request.get(
      additionalService.detail
        .replace('{orderDetailId}', orderDetailId)
        .replace('{id}', id)
    );
    return analysisRes(res);
  },

  /**
   * 支付追加服务订单
   * @param {number} orderDetailId 订单详情ID
   * @param {number} id 追加服务订单ID
   * @param {number} customerId 客户ID
   * @returns {Promise<any>} 返回支付结果
   */
  async pay(orderDetailId, id, customerId) {
    const res = await request.post(
      additionalService.pay
        .replace('{orderDetailId}', orderDetailId)
        .replace('{id}', id),
      { customerId }
    );
    return analysisRes(res);
  },



  /**
   * 获取可用权益卡
   * @param {number} customerId 客户ID
   * @param {number} serviceId 服务ID（可选，用于筛选适用的权益卡）
   * @returns {Promise<any>} 返回可用权益卡列表
   */
  async getAvailableCards(customerId, serviceId = null) {
    const params = serviceId ? { serviceId } : {};
    const res = await request.get(
      additionalService.availableCards.replace('{customerId}', customerId),
      params
    );
    return analysisRes(res);
  },

  /**
   * 获取可用优惠券
   * @param {number} customerId 客户ID
   * @param {number} serviceId 服务ID（可选，用于筛选适用的优惠券）
   * @returns {Promise<any>} 返回可用优惠券列表
   */
  async getAvailableCoupons(customerId, serviceId = null) {
    const params = serviceId ? { serviceId } : {};
    const res = await request.get(
      additionalService.availableCoupons.replace('{customerId}', customerId),
      params
    );
    return analysisRes(res);
  }
};
