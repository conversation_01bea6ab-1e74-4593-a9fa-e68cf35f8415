.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

/* 已选服务区域特殊样式 */
.selected-section {
  border: 2rpx solid #007aff;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.1);
  animation: highlight 0.3s ease-in-out;
}

@keyframes highlight {
  0% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.1);
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.selected-title {
  display: flex;
  align-items: center;
  gap: 10rpx;
  color: #007aff;
}

.service-count {
  font-size: 24rpx;
  color: #666;
  background-color: #f0f8ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 服务分组样式 */
.service-groups {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.service-group {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.group-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
  padding: 10rpx 0;
  border-bottom: 2rpx solid #e5e5e5;
}

/* 服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.service-item:active {
  background-color: #f0f0f0;
  border-color: #007aff;
}

.service-item.selected {
  border-color: #007aff;
  background-color: #f0f8ff;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}

.service-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.service-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.service-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}

.service-desc {
  font-size: 24rpx;
  color: #666;
}

.service-price {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: bold;
}

.service-action {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
  padding-bottom: 10rpx;
}

.add-btn.selected {
  background-color: #34c759;
}

.selected-badge {
  background-color: #ff6b35;
  color: #fff;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  position: relative;
  top: -10rpx;
  right: -10rpx;
}

.selected-count {
  font-size: 20rpx;
  font-weight: bold;
}

/* 已选服务样式 */
.selected-services {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.selected-service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.quantity-btn {
  width: 50rpx;
  height: 50rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  padding-bottom: 6rpx;
}

.quantity {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  min-width: 40rpx;
  text-align: center;
}

/* 优惠选择样式 */
.discount-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
}

.discount-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.discount-label {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.discount-desc {
  font-size: 24rpx;
  color: #666;
}

.arrow {
  font-size: 32rpx;
  color: #999;
}

/* 备注输入样式 */
.remark-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
}

/* 价格汇总样式 */
.price-summary {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.price-row:not(:last-child) {
  border-bottom: 1rpx solid #f0f0f0;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.price-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.price-row.discount .price-value {
  color: #ff6b35;
}

.price-row.total {
  padding-top: 20rpx;
  border-top: 2rpx solid #e5e5e5;
}

.price-row.total .price-label {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.total-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 提交按钮样式 */
.submit-section {
  padding: 20rpx 0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:disabled {
  background-color: #ccc;
}

/* 优惠选择弹窗样式 */
.discount-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.discount-modal-content {
  width: 100%;
  max-height: 80%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #999;
}

.discount-category {
  margin-bottom: 30rpx;
}

.category-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.discount-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.discount-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  margin-bottom: 15rpx;
}

.discount-item.selected {
  border-color: #007aff;
  background-color: #f0f8ff;
}

.discount-item-left {
  flex: 1;
}

.discount-item-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #333;
}

.discount-item-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.discount-item-date {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.discount-item-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 20rpx;
}

.discount-item-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 10rpx;
}

.discount-item-select {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 1rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
  background-color: #f5f5f5;
}

.discount-item-select.selected {
  background-color: #007aff;
  border-color: #007aff;
  color: #fff;
}

.discount-item-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.discount-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.selected-icon {
  color: #007aff;
  font-size: 32rpx;
  font-weight: bold;
}

.modal-actions {
  padding-top: 30rpx;
  border-top: 1rpx solid #e5e5e5;
}

.modal-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}

.confirm-btn {
  background-color: #007aff;
  color: #fff;
}

/* 浮动已选服务提示 */
.floating-summary {
  position: fixed;
  top: 200rpx;
  right: 30rpx;
  z-index: 100;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.summary-content {
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  color: #fff;
  padding: 20rpx 24rpx;
  border-radius: 50rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  min-width: 120rpx;
  backdrop-filter: blur(10rpx);
}

.summary-text {
  font-size: 24rpx;
  font-weight: bold;
  white-space: nowrap;
}

.summary-price {
  font-size: 28rpx;
  font-weight: bold;
}
