.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

/* 已选服务区域特殊样式 */
.selected-section {
  border: 2rpx solid #007aff;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.1);
  animation: highlight 0.3s ease-in-out;
}

@keyframes highlight {
  0% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.1);
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.selected-title {
  display: flex;
  align-items: center;
  gap: 10rpx;
  color: #007aff;
}

.service-count {
  font-size: 24rpx;
  color: #666;
  background-color: #f0f8ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 服务分组样式 */
.service-groups {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.service-group {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.group-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
  padding: 10rpx 0;
  border-bottom: 2rpx solid #e5e5e5;
}

/* 服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.service-item:active {
  background-color: #f0f0f0;
  border-color: #007aff;
}

.service-item.selected {
  border-color: #007aff;
  background-color: #f0f8ff;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}

.service-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.service-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.service-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}

.service-desc {
  font-size: 24rpx;
  color: #666;
}

.service-price {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: bold;
}

.service-action {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
  padding-bottom: 10rpx;
}

.add-btn.selected {
  background-color: #34c759;
}

.selected-badge {
  background-color: #ff6b35;
  color: #fff;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  position: relative;
  top: -10rpx;
  right: -10rpx;
}

.selected-count {
  font-size: 20rpx;
  font-weight: bold;
}

/* 已选服务样式 */
.selected-services {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.selected-service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.quantity-btn {
  width: 50rpx;
  height: 50rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  padding-bottom: 6rpx;
}

.quantity {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  min-width: 40rpx;
  text-align: center;
}

/* 优惠选择样式 */
.discount-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
}

.discount-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.discount-label {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.discount-desc {
  font-size: 24rpx;
  color: #666;
}

.arrow {
  font-size: 32rpx;
  color: #999;
}

/* 备注输入样式 */
.remark-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
}

/* 价格汇总样式 */
.price-summary {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.price-row:not(:last-child) {
  border-bottom: 1rpx solid #f0f0f0;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.price-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.price-row.discount .price-value {
  color: #ff6b35;
}

.price-row.total {
  padding-top: 20rpx;
  border-top: 2rpx solid #e5e5e5;
}

.price-row.total .price-label {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.total-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 提交按钮样式 */
.submit-section {
  padding: 20rpx 0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:disabled {
  background-color: #ccc;
}

/* 优惠选择弹窗样式 */
.discount-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.discount-modal-content {
  width: 100%;
  max-height: 80%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #999;
}

.discount-category {
  margin-bottom: 30rpx;
}

.category-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.discount-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.discount-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #fff6f9 0%, #fff 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 2rpx solid #f0f0f0;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  transform: translateY(0);
}

.discount-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(255, 79, 143, 0.1);
}

.discount-item::before {
  content: "";
  position: absolute;
  left: -6rpx;
  top: 50%;
  width: 16rpx;
  height: 40rpx;
  background-color: #ff4f8f;
  border-radius: 0 12rpx 12rpx 0;
  transform: translateY(-50%);
}

.discount-item::after {
  content: "";
  position: absolute;
  right: -6rpx;
  top: 50%;
  width: 16rpx;
  height: 40rpx;
  background-color: #ff4f8f;
  border-radius: 12rpx 0 0 12rpx;
  transform: translateY(-50%);
}

.discount-item.selected {
  border-color: #ff4f8f;
  background: linear-gradient(135deg, #fff1f6 0%, #fff6f9 100%);
  box-shadow: 0 6rpx 16rpx rgba(255, 79, 143, 0.2);
}

.discount-item-left {
  flex: 1;
  position: relative;
  z-index: 1;
}

.discount-item-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  color: #333;
}

.discount-item-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  background: rgba(255, 79, 143, 0.05);
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.discount-item-date {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.discount-item-tip {
  font-size: 24rpx;
  color: #ff4f8f;
  margin-top: 8rpx;
  font-weight: 500;
}

.discount-item-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  margin-left: 20rpx;
  position: relative;
  z-index: 1;
}

.discount-item-value {
  font-size: 40rpx;
  color: #ff4f8f;
  font-weight: bold;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(255, 79, 143, 0.2);
}

.discount-item-select {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background-color: #ff4f8f;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 79, 143, 0.3);
  transition: all 0.2s ease;
}

.discount-item:not(.selected) .discount-item-select {
  background-color: #f0f0f0;
  color: transparent;
  border: 2rpx dashed #ccc;
  box-shadow: none;
}

.discount-item-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.discount-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.selected-icon {
  color: #007aff;
  font-size: 32rpx;
  font-weight: bold;
}

.modal-actions {
  padding-top: 30rpx;
  border-top: 1rpx solid #e5e5e5;
}

.modal-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}

.confirm-btn {
  background-color: #007aff;
  color: #fff;
}

/* 浮动已选服务提示 */
.floating-summary {
  position: fixed;
  top: 200rpx;
  right: 30rpx;
  z-index: 100;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.summary-content {
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  color: #fff;
  padding: 20rpx 24rpx;
  border-radius: 50rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  min-width: 120rpx;
  backdrop-filter: blur(10rpx);
}

.summary-text {
  font-size: 24rpx;
  font-weight: bold;
  white-space: nowrap;
}

.summary-price {
  font-size: 28rpx;
  font-weight: bold;
}

.summary-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

/* 已有申请提示样式 */
.existing-notice {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  margin: 20rpx;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1rpx solid #ffc107;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.2);
}

.notice-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
  margin-top: 5rpx;
}

.notice-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.notice-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #856404;
}

.notice-desc {
  font-size: 24rpx;
  color: #856404;
  line-height: 1.4;
}

/* 禁用状态样式 */
.service-item.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.quantity-btn.disabled {
  opacity: 0.4;
  pointer-events: none;
  background-color: #f5f5f5;
  color: #ccc;
}

.remark-input.disabled {
  background-color: #f5f5f5;
  color: #999;
}

.submit-btn.disabled {
  background-color: #e0e0e0;
  color: #999;
  border: 1rpx solid #ccc;
}

.view-list-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: 2rpx solid #007aff;
  background-color: transparent;
  color: #007aff;
  margin-top: 20rpx;
}
