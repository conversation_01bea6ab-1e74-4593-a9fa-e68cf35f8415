import request, { analysisRes } from '../request';
import config from '../config';

const { apiUrls } = config;
const { employeePromotion } = apiUrls;

export default {
  /**
   * 检查推广码是否可用
   * @param {string} promotionCode 推广码
   * @returns {Promise<any>} 返回检查结果
   */
  async checkCode(promotionCode) {
    const res = await request.get(
      employeePromotion.checkCode.replace('{promotionCode}', promotionCode)
    );
    return analysisRes(res);
  },

  /**
   * 填写推广码建立推广关系
   * @param {object} data 推广数据
   * @param {number} data.customerId 客户ID
   * @param {string} data.promotionCode 推广码
   * @returns {Promise<any>} 返回创建结果
   */
  async create(data) {
    const res = await request.post(employeePromotion.create, data);
    return analysisRes(res);
  },

  /**
   * 查看关联的推广员工
   * @param {number} customerId 客户ID
   * @returns {Promise<any>} 返回推广员工信息
   */
  async getEmployee(customerId) {
    const res = await request.get(
      employeePromotion.getEmployee.replace('{customerId}', customerId)
    );
    return analysisRes(res);
  }
};
