// components/custom-navbar/custom-navbar.js
import dictionaryApi from "../../api/modules/dictionary.js";

Component({
  properties: {
    currentCode: {
      type: String,
      value: '',
      observer: function(newVal, oldVal) {
        // 当外部传入的 currentCode 发生变化时，更新组件内部状态
        if (newVal !== oldVal && newVal !== this.data.currentCode) {
          this.setData({
            currentCode: newVal
          });
        }
      }
    },
  },
  options: {
    // 允许组件接受外部样式类
    addGlobalClass: true,
    // 启用插槽
    multipleSlots: true,
  },

  data: {
    tabsDatas: [],
    currentCode: ''
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached() {
      // 在组件实例进入页面节点树时执行
      this.getCategories();
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    getCategories() {
      dictionaryApi.list("服务类型").then((list) => {
        const tabsDatas = list.map((item) => ({
          text: "约" + item.name,
          code: item.code,
        }));
        // 设置数据并默认选中传递的数据或第一项
        const code = this.properties.currentCode || this.data.currentCode || tabsDatas[0]?.code
        this.setData({
          tabsDatas,
          currentCode: code || "",
        });
        // 只有当有有效的 code 时才触发事件
        if (code) {
          this.triggerEvent("tabchange", {
            code: code
          });
        }
      });
    },

    changeTabs(evt) {
      const {
        code
      } = evt.currentTarget.dataset;
      if (code === this.data.currentCode) return;
      this.setData({
        currentCode: code,
      });
      // 触发自定义事件，将code传递给父页面
      this.triggerEvent("tabchange", {
        code
      });
    },
  },
});