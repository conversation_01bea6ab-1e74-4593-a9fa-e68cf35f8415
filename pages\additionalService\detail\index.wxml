<view class="container">
  <block wx:if="{{!loading && serviceDetail}}">
    <!-- 状态卡片 -->
    <view class="status-card">
      <view class="status-icon">{{serviceDetail.statusInfo.icon}}</view>
      <view class="status-info">
        <text class="status-text" style="color: {{serviceDetail.statusInfo.color}}">
          {{serviceDetail.statusInfo.text}}
        </text>
        <text class="status-desc">{{serviceDetail.statusInfo.desc}}</text>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <view class="section-title">基本信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">订单号</text>
          <text class="info-value">{{serviceDetail.sn}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">申请时间</text>
          <text class="info-value">{{serviceDetail.createdAt}}</text>
        </view>
        <view wx:if="{{serviceDetail.confirmTime}}" class="info-item">
          <text class="info-label">确认时间</text>
          <text class="info-value">{{serviceDetail.confirmTime}}</text>
        </view>
      </view>
    </view>

    <!-- 服务详情 -->
    <view class="info-section">
      <view class="section-title">服务详情</view>
      <view class="service-list">
        <view wx:for="{{serviceDetail.details}}" wx:key="id" class="service-item">
          <view class="service-info">
            <text class="service-name">{{item.serviceName}}</text>
            <text class="service-spec">数量：{{item.quantity}} | 单价：¥{{item.servicePrice}}</text>
          </view>
          <text class="service-total">¥{{item.servicePrice * item.quantity}}</text>
        </view>
      </view>
    </view>

    <!-- 价格明细 -->
    <view class="info-section">
      <view class="section-title">价格明细</view>
      <view class="price-details">
        <view class="price-row">
          <text class="price-label">服务原价</text>
          <text class="price-value">¥{{serviceDetail.originalPrice}}</text>
        </view>
        <view wx:if="{{serviceDetail.cardDeduction > 0}}" class="price-row discount">
          <text class="price-label">权益卡优惠</text>
          <text class="price-value">-¥{{serviceDetail.cardDeduction}}</text>
        </view>
        <view wx:if="{{serviceDetail.couponDeduction > 0}}" class="price-row discount">
          <text class="price-label">优惠券优惠</text>
          <text class="price-value">-¥{{serviceDetail.couponDeduction}}</text>
        </view>
        <view class="price-row total">
          <text class="price-label">实付金额</text>
          <text class="price-value total-price">¥{{serviceDetail.totalFee}}</text>
        </view>
      </view>
    </view>

    <!-- 优惠信息 -->
    <view wx:if="{{serviceDetail.discountInfos && serviceDetail.discountInfos.length > 0}}" class="info-section">
      <view class="section-title">优惠信息</view>
      <view class="discount-list">
        <view wx:for="{{serviceDetail.discountInfos}}" wx:key="id" class="discount-item">
          <view class="discount-info">
            <text class="discount-type">
              {{item.discountType === 'membership_card' ? '权益卡' : '优惠券'}}
            </text>
            <text class="discount-amount">优惠金额：¥{{item.discountAmount}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 客户信息 -->
    <view wx:if="{{serviceDetail.orderDetail && serviceDetail.orderDetail.order}}" class="info-section">
      <view class="section-title">客户信息</view>
      <view class="customer-info">
        <view class="info-item">
          <text class="info-label">客户姓名</text>
          <text class="info-value">{{serviceDetail.orderDetail.order.customer.name}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">联系电话</text>
          <text class="info-value">{{serviceDetail.orderDetail.order.customer.phone}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">主订单号</text>
          <text class="info-value link" bindtap="viewOrderDetail">
            {{serviceDetail.orderDetail.order.sn}}
          </text>
        </view>
      </view>
    </view>

    <!-- 备注信息 -->
    <view wx:if="{{serviceDetail.remark}}" class="info-section">
      <view class="section-title">备注信息</view>
      <view class="remark-content">
        <text class="remark-text">{{serviceDetail.remark}}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <block wx:if="{{serviceDetail.status === 'confirmed'}}">
        <button class="action-btn primary" bindtap="goPay">立即支付</button>
      </block>
      <button class="action-btn secondary" bindtap="contactService">联系客服</button>
    </view>
  </block>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 自定义模态框 -->
  <custom-modal
    show="{{showModal}}"
    title="{{modalTitle}}"
    content="{{modalContent}}"
    buttons="{{modalButtons}}"
    bind:confirm="handleModalConfirm"
    bind:cancel="handleModalCancel"
    bind:modalConfirm="handleModalConfirm"
    bind:modalCancel="handleModalCancel"
  ></custom-modal>
</view>
