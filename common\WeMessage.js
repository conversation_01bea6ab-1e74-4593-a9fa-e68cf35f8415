/**
 * 消息订阅
 */

import { OrderStatus } from './constant';
import messageApi from '../api/modules/weMessage';

class WeMessage {
  constructor(openId, sn, orderStatus) {
    this.openId = openId;
    this.sn = sn;
    this.orderStatus = orderStatus;
    this.templateIds = {
      接单成功通知: 'LSBwIETgaGdTBEOSE2jWc9H4kSqV6kmaLbYubaj98Gk',
      服务时间更改通知: '93CbZXFuxibQs9HeYo0u2tZSj-lLyPj6CZK73878N98',
      服务进度提醒: 'aSWstKk_cnoJDW9g9NMlbuW1cHCt_VV2zfwjRIePDqA',
      服务完成通知: 'AvjwdSi9u2BhNTpwBSghM8tnaXwoDfHTY-xBg-rQ3WI',
    };
  }

  /** 订单支付成功后调用此函数 */
  handlePaymentSuccess() {
    if (this.orderStatus === OrderStatus.待接单) {
      return {
        modalConfig: {
          title: '接单提醒',
          content: '您的订单已支付成功，是否接收商家接单提醒？',
          buttons: [
            {
              text: '暂不需要',
              type: 'cancel',
              event: 'modalCancel',
            },
            {
              text: '同意接收',
              type: 'primary',
              event: 'modalConfirm',
            },
          ],
        },
      };
    }
    return null;
  }

  /** 请求订阅接单提醒 */
  requestOrderConfirmationSubscribe() {
    wx.requestSubscribeMessage({
      tmplIds: [
        this.templateIds.接单成功通知,
        this.templateIds.服务时间更改通知,
        this.templateIds.服务进度提醒,
        // this.templateIds.服务完成通知,
      ], // 替换为你的接单提醒模板ID，最多支持3个
      success: async res => {
        await this.saveSubscriptionStatus(res);
      },
      fail: err => {
        console.error('订阅消息请求失败:', err);
        wx.showToast({
          title: '订阅失败，请稍后再试',
          icon: 'none',
        });
      },
    });
  }

  /** 保存订阅状态到服务器 */
  async saveSubscriptionStatus(res) {
    console.log ('保存订阅状态:', res);
    const openId = this.openId;
    const sn = this.sn;
    [this.templateIds.接单成功通知, this.templateIds.服务时间更改通知, this.templateIds.服务进度提醒].forEach(
      templateId => {
        messageApi.subscription(openId, sn, res[templateId] === 'accept', templateId);
      }
    );
  }
}

export default WeMessage;
