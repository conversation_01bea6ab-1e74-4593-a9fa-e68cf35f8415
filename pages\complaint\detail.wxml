<!-- pages/complaint/detail.wxml -->
<wxs module="utils">
function formatTime(timestamp) {
  var date = getDate(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

module.exports.formatTime = formatTime;
</wxs>

<view class="container" wx:if="{{!loading}}">
  <view wx:if="{{complaintDetail}}">
    <!-- 状态卡片 -->
    <view class="status-card">
      <view class="status-header">
        <view class="status-info">
          <text class="status-text" style="color: {{statusMap[complaintDetail.status].color}}">{{statusMap[complaintDetail.status].text}}</text>
          <text class="category-tag {{complaintDetail.category}}">{{categoryMap[complaintDetail.category]}}</text>
        </view>
        <text class="subcategory-text">{{subCategoryMap[complaintDetail.subCategory]}}</text>
      </view>
      <text class="status-desc">{{complaintDetail.result || statusMap[complaintDetail.status].desc}}</text>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <view class="section-title">
        <text>基本信息</text>
      </view>
      <view class="info-item">
        <text class="info-label">标题：</text>
        <text class="info-value">{{complaintDetail.title}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">详细描述：</text>
        <text class="info-value content">{{complaintDetail.content}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">联系方式：</text>
        <text class="info-value">{{complaintDetail.contactInfo}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">提交时间：</text>
        <text class="info-value">{{utils.formatTime(complaintDetail.createdAt)}}</text>
      </view>
    </view>

    <!-- 关联订单 -->
    <view class="order-section" wx:if="{{complaintDetail.orderId && complaintDetail.orderInfo}}">
      <view class="section-title">
        <text>关联订单</text>
      </view>
      <view class="order-card">
        <image class="service-image" src="{{complaintDetail.orderInfo.orderDetails[0].service.logo}}" mode="aspectFill"></image>
        <view class="order-info">
          <text class="service-name">{{complaintDetail.orderInfo.orderDetails[0].service.serviceName}}</text>
          <text class="order-sn">订单号：{{complaintDetail.orderInfo.sn}}</text>
          <text class="order-price">¥{{complaintDetail.orderInfo.totalFee}}</text>
        </view>
      </view>
    </view>

    <!-- 图片附件 -->
    <view class="photo-section" wx:if="{{complaintDetail.photoURLs && complaintDetail.photoURLs.length > 0}}">
      <view class="section-title">
        <text>图片附件</text>
      </view>
      <view class="photo-container">
        <image 
          wx:for="{{complaintDetail.photoURLs}}" 
          wx:key="index" 
          src="{{item}}" 
          class="photo-item" 
          mode="aspectFill"
          bindtap="previewImage"
          data-url="{{item}}"
        ></image>
      </view>
    </view>

    <!-- 处理结果 -->
    <view class="result-section" wx:if="{{complaintDetail.result && complaintDetail.status === 'resolved'}}">
      <view class="section-title">
        <text>处理结果</text>
      </view>
      <view class="result-card">
        <text class="result-content">{{complaintDetail.result}}</text>
        <view class="result-footer" wx:if="{{complaintDetail.handledAt}}">
          <text class="handle-time">处理时间：{{utils.formatTime(complaintDetail.handledAt)}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section" wx:if="{{complaintDetail.status === 'pending'}}">
      <button class="action-btn edit" bindtap="editComplaint">编辑</button>
      <button class="action-btn delete" bindtap="deleteComplaint">删除</button>
    </view>

    <!-- 联系客服 -->
    <view class="contact-section">
      <button class="contact-btn" bindtap="contactService">联系客服</button>
    </view>
  </view>

  <!-- 数据为空 -->
  <view class="empty-state" wx:else>
    <image class="empty-image" src="https://xian7.zos.ctyun.cn/pet/static/empty.png" mode="aspectFit"></image>
    <text class="empty-text">投诉建议不存在</text>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-spinner"></view>
  <text class="loading-text">加载中...</text>
</view>
