<view class="register-container">
  <form bindsubmit="handleSubmit">
    <!-- 头像区域 -->
    <view class="avatar-section">
      <!-- <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
        <image class="avatar" src="{{formData.avatarUrl || siteinfo.constant.defalutAvatar1}}" mode="aspectFill"></image>
        <text class="tips">点击更换头像</text>
      </button> -->
      <button class="avatar-wrapper">
        <image class="avatar" src="{{siteinfo.constant.defalutAvatar1}}" mode="aspectFill"></image>
      </button>
      <!-- 隐藏的input用于表单提交 -->
      <input type="text" name="avatarUrl" value="{{formData.avatarUrl || siteinfo.constant.defalutAvatar1}}" style="display: none;" />
      <input type="text" name="openid" value="{{formData.openid}}" style="display: none;" />
    </view>

    <!-- 授权按钮区域 -->
    <!-- <view class="auth-section">
      <button class="auth-btn" open-type="getUserInfo" bindtap="getUserProfile">微信授权</button>
    </view> -->

    <!-- 表单区域 -->
    <view class="form-section">
      <!-- 昵称 -->
      <view class="form-item">
        <text class="label required">昵称</text>
        <input class="input" name="nickName" value="{{formData.nickName}}" placeholder="请输入昵称" maxlength="12" bindinput="onNickNameChange" />
      </view>

      <!-- 手机号 -->
      <view class="form-item">
        <text class="label required">手机号</text>
        <input class="input" name="phone" value="{{formData.phone}}" />
        <button class="getPhoneNumber" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">获取手机号</button>
      </view>

      <!-- 性别 -->
      <view class="form-item">
        <text class="label">性别</text>
        <radio-group class="radio-group" name="gender" bindchange="onGenderChange">
          <label class="radio-label">
            <radio value="1" checked="{{formData.gender === '1'}}" />男
          </label>
          <label class="radio-label">
            <radio value="2" checked="{{formData.gender === '2'}}" />女
          </label>
          <label class="radio-label">
            <radio value="0" checked="{{formData.gender === '0'}}" />保密
          </label>
        </radio-group>
      </view>

      <!-- 推广码 -->
      <view class="form-item">
        <text class="label">推广码</text>
        <view class="promotion-code-wrapper">
          <input
            class="input"
            name="promotionCode"
            value="{{formData.promotionCode}}"
            placeholder="请输入员工推广码（选填）"
            maxlength="20"
            bindinput="onPromotionCodeChange"
          />
          <view wx:if="{{isCheckingCode}}" class="checking-status">验证中...</view>
          <view wx:elif="{{promotionCodeInfo && formData.promotionCode}}" class="code-status">
            <text wx:if="{{promotionCodeInfo.available}}" class="success">
              ✓ {{promotionCodeInfo.employee.name}}
            </text>
            <text wx:else class="error">
              ✗ {{promotionCodeInfo.message}}
            </text>
          </view>
        </view>
      </view>

    </view>

    <!-- 提交按钮 -->
    <!-- 未同意协议时显示的按钮 -->
    <button wx:if="{{globalData.agree !== '1'}}" class="submit-btn" bindtap="handleAgreeFirst">提交注册</button>
    <!-- 已同意协议时显示的按钮 -->
    <button wx:if="{{globalData.agree === '1'}}" class="submit-btn" form-type="submit">提交注册</button>

  </form>
  <view class="tip">
    <view class="agreed" catchtap="navigateTo" data-type="agreeFunction">
      <text wx:if="{{globalData.agree=='1'}}" class="flex icon3 diygw-col-0 icon3-clz diy-icon-roundcheck"></text>
      <text wx:if="{{globalData.agree!='1'}}" class="flex icon2 diygw-col-0 icon2-clz diy-icon-round"></text>
      <text class="diygw-col-0"> 我已阅读并同意</text>
    </view>
    <text catchtap="navigateTo" data-type="page" data-url="/pages/mine/userAgreement/index" data-newstype="privacy" class="text-link"> 《贝宠乐福用户协议》</text>
    <text catchtap="navigateTo" data-type="page" data-url="/pages/mine/privacyAgreement/index" data-newstype="user" class="text-link"> 《贝宠乐福隐私协议》</text>
    <text> 未注册手机号，登录成功将自动注册贝宠乐福账号 </text>
  </view>
</view>