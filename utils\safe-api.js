/**
 * 安全的API调用工具
 * 用于避免JSBridge错误和提供更好的错误处理
 */

/**
 * 安全的API调用包装器
 * @param {Function} apiCall API调用函数
 * @param {Object} options 选项
 * @param {number} options.retries 重试次数，默认3次
 * @param {number} options.delay 重试延迟，默认1000ms
 * @param {string} options.errorMessage 错误提示信息
 * @returns {Promise} API调用结果
 */
async function safeApiCall(apiCall, options = {}) {
  const {
    retries = 3,
    delay = 1000,
    errorMessage = 'API调用失败'
  } = options;

  let lastError = null;

  for (let i = 0; i < retries; i++) {
    try {
      // 添加延迟，避免JSBridge错误
      if (i > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      const result = await apiCall();
      return result;
    } catch (error) {
      lastError = error;
      console.warn(`API调用失败，第${i + 1}次重试:`, error);

      // 如果是最后一次重试，抛出错误
      if (i === retries - 1) {
        throw new Error(`${errorMessage}: ${error.message || error}`);
      }
    }
  }

  throw lastError;
}

/**
 * 安全的并行API调用
 * @param {Array} apiCalls API调用数组
 * @param {Object} options 选项
 * @returns {Promise} 所有API调用结果
 */
async function safeParallelApiCalls(apiCalls, options = {}) {
  const {
    maxConcurrency = 2, // 限制并发数，避免JSBridge错误
    errorMessage = '批量API调用失败'
  } = options;

  const results = [];
  
  // 分批执行，避免过多并发请求
  for (let i = 0; i < apiCalls.length; i += maxConcurrency) {
    const batch = apiCalls.slice(i, i + maxConcurrency);
    
    try {
      const batchResults = await Promise.all(
        batch.map(apiCall => safeApiCall(apiCall, { errorMessage }))
      );
      results.push(...batchResults);
    } catch (error) {
      console.error('批量API调用失败:', error);
      throw error;
    }

    // 批次间添加延迟
    if (i + maxConcurrency < apiCalls.length) {
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }

  return results;
}

/**
 * 安全的串行API调用
 * @param {Array} apiCalls API调用数组
 * @param {Object} options 选项
 * @returns {Promise} 所有API调用结果
 */
async function safeSerialApiCalls(apiCalls, options = {}) {
  const {
    delay = 200,
    errorMessage = '串行API调用失败'
  } = options;

  const results = [];

  for (let i = 0; i < apiCalls.length; i++) {
    try {
      const result = await safeApiCall(apiCalls[i], { errorMessage });
      results.push(result);

      // 调用间添加延迟
      if (i < apiCalls.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    } catch (error) {
      console.error(`第${i + 1}个API调用失败:`, error);
      throw error;
    }
  }

  return results;
}

/**
 * 检查网络状态
 * @returns {Promise<boolean>} 网络是否可用
 */
function checkNetworkStatus() {
  return new Promise((resolve) => {
    wx.getNetworkType({
      success: (res) => {
        resolve(res.networkType !== 'none');
      },
      fail: () => {
        resolve(false);
      }
    });
  });
}

/**
 * 安全的页面跳转
 * @param {Object} options 跳转选项
 */
function safeNavigate(options) {
  try {
    const { type = 'navigateTo', url, ...otherOptions } = options;
    
    if (!url) {
      console.error('跳转URL不能为空');
      return;
    }

    const navigateMethod = wx[type] || wx.navigateTo;
    navigateMethod({
      url,
      ...otherOptions,
      fail: (error) => {
        console.error('页面跳转失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  } catch (error) {
    console.error('页面跳转异常:', error);
  }
}

export {
  safeApiCall,
  safeParallelApiCalls,
  safeSerialApiCalls,
  checkNetworkStatus,
  safeNavigate
};

export default {
  safeApiCall,
  safeParallelApiCalls,
  safeSerialApiCalls,
  checkNetworkStatus,
  safeNavigate
};
