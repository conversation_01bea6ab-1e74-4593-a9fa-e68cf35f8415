import request, { analysisRes } from '../request';
import config from '../config';

const { zos } = config.apiUrls;

export default {
  // getUploadLink: '/openapi/zos/upload-link', // 获取上传链接
  async getUploadLink(key) {
    const res = await request.get(zos.getUploadLink, {
      key,
    });
    console.log('getUploadLink res: ', res);
    return analysisRes(res);
  },

  // getUploadLink: '/openapi/zos/set-object-headers', // 设置对象的 HTTP 头
  async setObjectHeaders(key) {
    const res = await request.post(zos.setObjHeaders, {
      key,
      ContentType: 'image/png',
    });
    console.log('setObjectHeaders res: ', res);
    return analysisRes(res);
  },

  // getUploadLink: '/openapi/zos/set-object-acl', // 获设置对象ACL
  async setObjectACL(key) {
    const res = await request.post(zos.setObjAcl, {
      key,
      ACL: 'public-read',
    });
    console.log('setObjectACL res: ', res);
    return analysisRes(res);
  },

  getLink(key) {
    return `https://${config.zos_endpoint}/${config.zos_bucket}/${key}`;
  },
};
