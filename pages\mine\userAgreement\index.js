// pages/mine/userAgreement/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    users: {
      title: '贝宠约洗用户协议',
      sections: [
        {
          title: '一、服务内容',
          subsections: [
            {
              title: '服务类型',
              content: [
                '通过移动洗护车提供上门宠物洗护、美容服务（包括基础清洁、毛发修剪、指甲护理等）',
                '可选增值服务（如驱虫、SPA护理、宠物用品销售等），具体以小程序页面展示为准',
              ],
            },
            {
              title: '服务范围',
              content: [
                "当前覆盖城市及区域详见小程序'服务范围'公示",
                '偏远地区或特殊环境（如无停车位、禁止车辆进入等）可能导致服务无法提供',
              ],
            },
            {
              title: '服务流程',
              content: [
                '用户需在小程序完成预约→支付→服务确认→评价的全流程操作',
                '订单生效以支付成功为准，未支付订单15分钟内自动取消',
              ],
            },
          ],
        },
        {
          title: '二、用户权利义务',
          subsections: [
            {
              title: '注册要求',
              content: [
                '用户需提供真实姓名、联系方式、服务地址、宠物信息（品种、年龄、疫苗接种记录、过敏史、攻击性行为记录等）',
                '如提供虚假信息导致服务纠纷，用户需承担全部责任',
              ],
            },
            {
              title: '服务规范',
              content: [
                '宠物健康：确保宠物无传染性疾病（如犬瘟、猫瘟）、未处于发情期或怀孕期',
                '安全交接：服务期间需将宠物安置于安全环境（如航空箱、牵引绳），并配合服务人员检查宠物状态',
                '禁止行为：不得辱骂、威胁服务人员，不得利用平台刷单、传播违法信息或恶意差评',
              ],
            },
            {
              title: '订单修改',
              content: [
                '取消订单：提前六个小时，扣除20%，提前四个小时，扣除40%，提前两个小时扣除60%，到点取消扣除80%',
                '改期服务：需提前12小时申请，每订单仅限免费改期1次，后续改期需支付10%服务费',
              ],
            },
          ],
        },
        {
          title: '三、责任限制',
          subsections: [
            {
              title: '宠物风险',
              content: [
                '因宠物自身健康问题（如心脏病、癫痫发作）或攻击性行为导致的伤害、逃脱等后果，用户需自行承担责任',
                '若宠物在服务过程中出现意外伤病，平台可协助送医，但医疗费用由用户承担',
              ],
            },
            {
              title: '服务争议',
              content: [
                '若因服务人员操作失误导致宠物受伤或物品损坏，经核实后平台将承担相应赔偿责任（最高不超过订单金额的200%）',
                '用户需在服务完成后48小时内提交争议申请，超时视为无异议',
              ],
            },
            {
              title: '不可抗力',
              content: [
                '包括但不限于自然灾害、交通管制、疫情封控、车辆故障等导致服务中断',
                '双方可协商延期或退款，平台不承担额外赔偿',
              ],
            },
          ],
        },
        {
          title: '四、协议变更与终止',
          subsections: [
            {
              title: '协议变更',
              content: [
                '平台将通过小程序公告、短信或邮件通知用户协议更新内容，用户若继续使用服务即视为同意新条款',
                '重大条款变更（如责任限制、违约金比例）将提供7日公示期',
              ],
            },
            {
              title: '协议终止',
              content: [
                '用户可随时注销账户，但已支付费用不予退还',
                '若用户违反协议（如多次恶意投诉、攻击服务人员），平台有权暂停或永久终止其账户',
              ],
            },
          ],
        },
        {
          title: '五、其他条款',
          subsections: [
            {
              title: '知识产权',
              content: ['小程序内所有内容（文字、图片、LOGO）版权归平台所有', '未经授权不得复制、传播或用于商业目的'],
            },
            {
              title: '争议解决',
              content: [
                '本协议适用中华人民共和国法律',
                '争议协商不成时，可向平台所在地（西安市雁塔区）人民法院提起诉讼',
              ],
            },
            {
              title: '未成年人使用',
              content: ['未成年人需在监护人陪同下使用服务', '监护人需对未成年人行为承担全部责任'],
            },
          ],
        },
      ],
      description:
        "本协议自用户勾选'同意'后生效，最终解释权归贝宠约洗所有。协议未尽事宜，以平台公示的《隐私政策》《服务规则》等补充条款为准。",
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '贝宠约洗，足不出户享精致洗护～',
      path: `/pages/index/index?aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share.png',
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },
  onShareTimeline: function () {
    return {
      title: '「忙到没空送洗？上门服务解忧！」贝宠约洗，专业到家超省心～',
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share1.png',
      query: `aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },
});
